//! # SF2 Sprite Graphics System
//! 
//! Manages authentic Street Fighter II sprite rendering using extracted ROM data.

use bevy::prelude::*;
use sf2_assets::{ExtractedSprites, SpriteExtractorResource};
use sf2_types::FighterId;
use crate::components::{Fighter, AnimationState};
use std::collections::HashMap;

/// Resource for managing SF2 character sprite textures
#[derive(Resource, Default)]
pub struct SF2SpriteManager {
    /// Character sprite textures indexed by fighter ID and animation frame
    pub character_textures: HashMap<FighterId, HashMap<String, Vec<Handle<Image>>>>,
    /// Loaded sprite metadata
    pub sprite_metadata: HashMap<u32, SF2SpriteMetadata>,
    /// Default fallback texture for missing sprites
    pub fallback_texture: Option<Handle<Image>>,
}

/// Metadata for SF2 sprites
#[derive(Debug, Clone)]
pub struct SF2SpriteMetadata {
    pub sprite_id: u32,
    pub fighter_id: FighterId,
    pub animation_name: String,
    pub frame_index: usize,
    pub width: u16,
    pub height: u16,
    pub offset_x: i16,
    pub offset_y: i16,
}

/// Component for SF2 character sprite rendering
#[derive(Component)]
pub struct SF2CharacterSprite {
    pub fighter_id: FighterId,
    pub current_animation: String,
    pub current_frame: usize,
    pub sprite_scale: Vec2,
    pub flip_x: bool,
}

/// Component to hold the texture handle for SF2 sprites
#[derive(Component)]
pub struct SF2TextureHandle(pub Handle<Image>);

impl Default for SF2CharacterSprite {
    fn default() -> Self {
        Self {
            fighter_id: FighterId::Ryu,
            current_animation: "idle".to_string(),
            current_frame: 0,
            sprite_scale: Vec2::new(2.0, 2.0), // 2x scale for better visibility
            flip_x: false,
        }
    }
}

/// SF2 sprite animation definitions
#[derive(Resource)]
pub struct SF2AnimationDefinitions {
    pub animations: HashMap<FighterId, HashMap<String, SF2Animation>>,
}

/// Animation definition for SF2 characters
#[derive(Debug, Clone)]
pub struct SF2Animation {
    pub name: String,
    pub frames: Vec<SF2AnimationFrame>,
    pub loop_animation: bool,
    pub frame_duration: f32, // Duration per frame in seconds
}

/// Individual animation frame
#[derive(Debug, Clone)]
pub struct SF2AnimationFrame {
    pub sprite_id: u32,
    pub duration_override: Option<f32>, // Override default frame duration
    pub offset: Vec2, // Additional offset for this frame
}

impl Default for SF2AnimationDefinitions {
    fn default() -> Self {
        let mut animations = HashMap::new();
        
        // Initialize basic animations for Ryu
        let mut ryu_animations = HashMap::new();
        ryu_animations.insert("idle".to_string(), SF2Animation {
            name: "idle".to_string(),
            frames: vec![
                SF2AnimationFrame { sprite_id: 0, duration_override: None, offset: Vec2::ZERO },
                SF2AnimationFrame { sprite_id: 1, duration_override: None, offset: Vec2::ZERO },
                SF2AnimationFrame { sprite_id: 2, duration_override: None, offset: Vec2::ZERO },
                SF2AnimationFrame { sprite_id: 3, duration_override: None, offset: Vec2::ZERO },
            ],
            loop_animation: true,
            frame_duration: 0.2, // 200ms per frame
        });
        
        ryu_animations.insert("walk_forward".to_string(), SF2Animation {
            name: "walk_forward".to_string(),
            frames: vec![
                SF2AnimationFrame { sprite_id: 16, duration_override: None, offset: Vec2::ZERO },
                SF2AnimationFrame { sprite_id: 17, duration_override: None, offset: Vec2::ZERO },
                SF2AnimationFrame { sprite_id: 18, duration_override: None, offset: Vec2::ZERO },
                SF2AnimationFrame { sprite_id: 19, duration_override: None, offset: Vec2::ZERO },
                SF2AnimationFrame { sprite_id: 20, duration_override: None, offset: Vec2::ZERO },
                SF2AnimationFrame { sprite_id: 21, duration_override: None, offset: Vec2::ZERO },
            ],
            loop_animation: true,
            frame_duration: 0.1, // 100ms per frame for walking
        });
        
        animations.insert(FighterId::Ryu, ryu_animations);
        
        // Initialize basic animations for Ken (similar to Ryu but different sprite IDs)
        let mut ken_animations = HashMap::new();
        ken_animations.insert("idle".to_string(), SF2Animation {
            name: "idle".to_string(),
            frames: vec![
                SF2AnimationFrame { sprite_id: 64, duration_override: None, offset: Vec2::ZERO },
                SF2AnimationFrame { sprite_id: 65, duration_override: None, offset: Vec2::ZERO },
                SF2AnimationFrame { sprite_id: 66, duration_override: None, offset: Vec2::ZERO },
                SF2AnimationFrame { sprite_id: 67, duration_override: None, offset: Vec2::ZERO },
            ],
            loop_animation: true,
            frame_duration: 0.2,
        });
        
        ken_animations.insert("walk_forward".to_string(), SF2Animation {
            name: "walk_forward".to_string(),
            frames: vec![
                SF2AnimationFrame { sprite_id: 80, duration_override: None, offset: Vec2::ZERO },
                SF2AnimationFrame { sprite_id: 81, duration_override: None, offset: Vec2::ZERO },
                SF2AnimationFrame { sprite_id: 82, duration_override: None, offset: Vec2::ZERO },
                SF2AnimationFrame { sprite_id: 83, duration_override: None, offset: Vec2::ZERO },
                SF2AnimationFrame { sprite_id: 84, duration_override: None, offset: Vec2::ZERO },
                SF2AnimationFrame { sprite_id: 85, duration_override: None, offset: Vec2::ZERO },
            ],
            loop_animation: true,
            frame_duration: 0.1,
        });
        
        animations.insert(FighterId::Ken, ken_animations);
        
        Self { animations }
    }
}

/// Plugin for SF2 sprite graphics system
pub struct SF2SpriteGraphicsPlugin;

impl Plugin for SF2SpriteGraphicsPlugin {
    fn build(&self, app: &mut App) {
        app
            .init_resource::<SF2SpriteManager>()
            .init_resource::<SF2AnimationDefinitions>()
            .add_systems(Update, (
                load_character_sprites_system,
                update_character_sprite_system,
                animate_character_sprites_system,
            ));
    }
}

/// System to load character sprites from extracted ROM data
fn load_character_sprites_system(
    mut sprite_manager: ResMut<SF2SpriteManager>,
    extracted_sprites: Res<ExtractedSprites>,
    sprite_extractor_res: Res<SpriteExtractorResource>,
    mut images: ResMut<Assets<Image>>,
    mut commands: Commands,
) {
    // Only load if we have extracted sprites and haven't loaded yet
    if extracted_sprites.sprites.is_empty() || !sprite_manager.character_textures.is_empty() {
        return;
    }

    if let Some(ref extractor) = sprite_extractor_res.extractor {
        info!("Loading SF2 character sprites from extracted ROM data");
        
        // Load first few sprites for testing (Ryu and Ken idle animations)
        let sprite_ids_to_load = vec![
            // Ryu sprites (0-31)
            0, 1, 2, 3, 16, 17, 18, 19, 20, 21,
            // Ken sprites (64-95) 
            64, 65, 66, 67, 80, 81, 82, 83, 84, 85,
        ];
        
        for sprite_id in sprite_ids_to_load {
            match extractor.extract_sprite(sprite_id) {
                Ok(extracted_sprite) => {
                    // Create Bevy image handle
                    let image_handle = images.add(extracted_sprite.bevy_image);
                    
                    // Determine fighter ID based on sprite ID ranges
                    let fighter_id = if sprite_id < 64 {
                        FighterId::Ryu
                    } else if sprite_id < 128 {
                        FighterId::Ken
                    } else {
                        continue; // Skip other characters for now
                    };
                    
                    // Store sprite metadata
                    sprite_manager.sprite_metadata.insert(sprite_id, SF2SpriteMetadata {
                        sprite_id,
                        fighter_id,
                        animation_name: if sprite_id % 64 < 16 { "idle" } else { "walk_forward" }.to_string(),
                        frame_index: (sprite_id % 16) as usize,
                        width: extracted_sprite.metadata.width,
                        height: extracted_sprite.metadata.height,
                        offset_x: extracted_sprite.metadata.offset_x,
                        offset_y: extracted_sprite.metadata.offset_y,
                    });
                    
                    // Store texture handle
                    sprite_manager.character_textures
                        .entry(fighter_id)
                        .or_insert_with(HashMap::new)
                        .entry("sprites".to_string())
                        .or_insert_with(Vec::new)
                        .push(image_handle);
                    
                    info!("Loaded sprite {} for {:?}", sprite_id, fighter_id);
                }
                Err(e) => {
                    warn!("Failed to extract sprite {}: {}", sprite_id, e);
                }
            }
        }
        
        info!("SF2 character sprite loading complete");
    }
}

/// System to update character sprite components
fn update_character_sprite_system(
    mut query: Query<(Entity, &Fighter, &mut SF2CharacterSprite), Added<Fighter>>,
    mut commands: Commands,
) {
    for (entity, fighter, mut sprite_component) in query.iter_mut() {
        // Initialize sprite component for new fighters
        sprite_component.fighter_id = fighter.fighter_id;
        sprite_component.current_animation = "idle".to_string();
        sprite_component.current_frame = 0;
        
        info!("Initialized SF2 sprite component for {:?}", fighter.fighter_id);
    }
}

/// System to animate character sprites
fn animate_character_sprites_system(
    mut query: Query<(&mut SF2CharacterSprite, &mut AnimationState, &mut Sprite, &mut SF2TextureHandle)>,
    sprite_manager: Res<SF2SpriteManager>,
    animation_defs: Res<SF2AnimationDefinitions>,
    time: Res<Time>,
) {
    for (mut sprite_component, mut anim_state, mut sprite, mut texture_handle) in query.iter_mut() {
        // Update animation timer
        anim_state.timer += time.delta_secs();
        
        // Get animation definition
        if let Some(fighter_animations) = animation_defs.animations.get(&sprite_component.fighter_id) {
            if let Some(animation) = fighter_animations.get(&sprite_component.current_animation) {
                // Check if we need to advance frame
                if anim_state.timer >= animation.frame_duration {
                    anim_state.timer = 0.0;
                    sprite_component.current_frame = (sprite_component.current_frame + 1) % animation.frames.len();
                    anim_state.frame = sprite_component.current_frame as u32;
                }
                
                // Update sprite texture if we have loaded textures
                if let Some(fighter_textures) = sprite_manager.character_textures.get(&sprite_component.fighter_id) {
                    if let Some(sprite_textures) = fighter_textures.get("sprites") {
                        if let Some(frame_data) = animation.frames.get(sprite_component.current_frame) {
                            // Find texture for this sprite ID
                            if let Some(sprite_metadata) = sprite_manager.sprite_metadata.get(&frame_data.sprite_id) {
                                // For now, use a simple mapping - this will be improved
                                let texture_index = (frame_data.sprite_id % 10) as usize;
                                if let Some(texture) = sprite_textures.get(texture_index) {
                                    texture_handle.0 = texture.clone();
                                    
                                    // Update sprite properties
                                    sprite.custom_size = Some(Vec2::new(
                                        sprite_metadata.width as f32 * sprite_component.sprite_scale.x,
                                        sprite_metadata.height as f32 * sprite_component.sprite_scale.y,
                                    ));
                                    
                                    // Apply horizontal flip if needed
                                    sprite.flip_x = sprite_component.flip_x;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
