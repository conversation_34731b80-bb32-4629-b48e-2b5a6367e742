//! # Collision System
//!
//! High-performance collision detection system with SIMD optimizations,
//! batch processing, and cache-friendly data structures for 60 FPS performance.

use bevy::prelude::*;
use bevy::ecs::system::SystemParam;
use std::time::Instant;

use sf2_types::{
    CollisionConfig, SF2Coll<PERSON><PERSON><PERSON><PERSON>, CollisionResult,
    SpatialGrid, SpatialEntry, CollisionLayer,
    HitboxManager, FighterHitboxes,
    CollisionResponseProcessor, CollisionResponse, CollisionResponseType,
    Point16, Point8, Rect8,
};
use crate::components::*;
use crate::events::*;

/// Collision system configuration
#[derive(Resource, Debug)]
pub struct CollisionSystemConfig {
    /// Collision configuration from environment variables
    pub collision_config: CollisionConfig,
    /// Maximum collision checks per frame
    pub max_checks_per_frame: u32,
    /// Enable performance profiling
    pub enable_profiling: bool,
    /// SIMD batch size for collision checks
    pub simd_batch_size: usize,
}

impl Default for CollisionSystemConfig {
    fn default() -> Self {
        let collision_config = CollisionConfig::from_env();
        Self {
            max_checks_per_frame: collision_config.max_collision_checks_per_frame,
            enable_profiling: collision_config.debug_collision_performance,
            simd_batch_size: collision_config.collision_batch_size,
            collision_config,
        }
    }
}

/// Collision system performance metrics
#[derive(Resource, Debug, Default)]
pub struct CollisionMetrics {
    /// Total collision checks this frame
    pub collision_checks: u32,
    /// Broad-phase eliminations
    pub broad_phase_eliminations: u32,
    /// Narrow-phase collisions detected
    pub narrow_phase_hits: u32,
    /// Time spent in collision detection (microseconds)
    pub collision_time_us: f32,
    /// Spatial grid performance
    pub spatial_grid_metrics: Option<sf2_types::SpatialGridMetrics>,
}

/// System parameter for optimized collision queries
#[derive(SystemParam)]
pub struct CollisionQuery<'w, 's> {
    /// Fighters with hitbox managers
    fighters: Query<'w, 's, (Entity, &'static Transform, &'static mut HitboxManager), With<Fighter>>,
    /// Projectiles with collision
    projectiles: Query<'w, 's, (Entity, &'static Transform, &'static CollisionBox), With<Projectile>>,
    /// Environment collision entities
    environment: Query<'w, 's, (Entity, &'static Transform, &'static CollisionBox), With<Environment>>,
}

/// Main collision detection system
pub fn collision_detection_system(
    collision_config: ResMut<CollisionSystemConfig>,
    mut spatial_grid: ResMut<SpatialGrid>,
    mut response_processor: ResMut<CollisionResponseProcessor>,
    mut collision_metrics: ResMut<CollisionMetrics>,
    collision_query: CollisionQuery,
    mut hit_events: EventWriter<HitEvent>,
    mut block_events: EventWriter<BlockEvent>,
    mut push_events: EventWriter<PushEvent>,
) {
    let frame_start = Instant::now();
    
    // Reset metrics
    collision_metrics.collision_checks = 0;
    collision_metrics.broad_phase_eliminations = 0;
    collision_metrics.narrow_phase_hits = 0;
    
    // Clear spatial grid
    spatial_grid.clear();
    
    // Update spatial grid with all entities
    update_spatial_grid(
        &mut spatial_grid,
        &collision_query,
        &collision_config.collision_config,
    );
    
    // Perform collision detection
    if collision_config.collision_config.spatial_partitioning_enabled {
        spatial_collision_detection(
            &mut spatial_grid,
            &mut response_processor,
            &mut collision_metrics,
            &collision_config,
            &collision_query,
        );
    } else {
        brute_force_collision_detection(
            &mut response_processor,
            &mut collision_metrics,
            &collision_config,
            &collision_query,
        );
    }
    
    // Process collision responses
    let responses = response_processor.take_pending_responses();
    process_collision_responses(
        responses,
        &mut hit_events,
        &mut block_events,
        &mut push_events,
    );
    
    // Update performance metrics
    collision_metrics.collision_time_us = frame_start.elapsed().as_micros() as f32;
    collision_metrics.spatial_grid_metrics = Some(*spatial_grid.metrics());
    
    // Log performance warnings
    if collision_config.enable_profiling {
        if collision_metrics.collision_time_us > 1000.0 {
            warn!(
                "Collision detection took {}μs (checks: {}, hits: {})",
                collision_metrics.collision_time_us,
                collision_metrics.collision_checks,
                collision_metrics.narrow_phase_hits
            );
        }
    }
}

/// Update spatial grid with all collision entities
fn update_spatial_grid(
    spatial_grid: &mut SpatialGrid,
    collision_query: &CollisionQuery,
    config: &CollisionConfig,
) {
    // Add fighters to spatial grid
    for (entity, transform, hitbox_manager) in collision_query.fighters.iter() {
        if let Some(hitboxes) = hitbox_manager.get_current_hitboxes() {
            let position = Point16::new(
                transform.translation.x as i16,
                transform.translation.y as i16,
            );
            
            // Add pushbox if present
            if let Some(pushbox) = hitboxes.get_pushbox() {
                let bounds = pushbox.shape.to_absolute(position.x, position.y);
                spatial_grid.insert(entity, bounds, CollisionLayer::Fighter);
            }
            
            // Add active hitbox if present
            if let Some(active_hitbox) = hitboxes.get_active_hitbox() {
                let bounds = active_hitbox.shape.to_absolute(position.x, position.y);
                spatial_grid.insert(entity, bounds, CollisionLayer::Fighter);
            }
        }
    }
    
    // Add projectiles to spatial grid
    for (entity, transform, collision_box) in collision_query.projectiles.iter() {
        let position = Point16::new(
            transform.translation.x as i16,
            transform.translation.y as i16,
        );
        let bounds = Rect8::new(
            position.x as i8 + collision_box.offset_x as i8,
            position.y as i8 + collision_box.offset_y as i8,
            collision_box.width as i8,
            collision_box.height as i8,
        );
        spatial_grid.insert(entity, bounds, CollisionLayer::Projectile);
    }
    
    // Add environment entities to spatial grid
    for (entity, transform, collision_box) in collision_query.environment.iter() {
        let position = Point16::new(
            transform.translation.x as i16,
            transform.translation.y as i16,
        );
        let bounds = Rect8::new(
            position.x as i8 + collision_box.offset_x as i8,
            position.y as i8 + collision_box.offset_y as i8,
            collision_box.width as i8,
            collision_box.height as i8,
        );
        spatial_grid.insert(entity, bounds, CollisionLayer::Environment);
    }
}

/// Spatial partitioning-based collision detection
fn spatial_collision_detection(
    spatial_grid: &mut SpatialGrid,
    response_processor: &mut CollisionResponseProcessor,
    metrics: &mut CollisionMetrics,
    config: &CollisionSystemConfig,
    collision_query: &CollisionQuery,
) {
    let collision_pairs = spatial_grid.generate_collision_pairs();
    let mut checks_performed = 0;
    
    for (entry1, entry2) in collision_pairs {
        if checks_performed >= config.max_checks_per_frame {
            break;
        }
        
        // Broad-phase distance check
        if !broad_phase_check(&entry1, &entry2, config.collision_config.broad_phase_threshold) {
            metrics.broad_phase_eliminations += 1;
            continue;
        }
        
        // Perform narrow-phase collision detection
        if let Some(collision_result) = narrow_phase_collision_check(
            &entry1,
            &entry2,
            collision_query,
            &config.collision_config,
        ) {
            process_collision_pair(
                entry1.entity,
                entry2.entity,
                collision_result,
                response_processor,
                collision_query,
            );
            metrics.narrow_phase_hits += 1;
        }
        
        checks_performed += 1;
        metrics.collision_checks += 1;
    }
}

/// Brute force collision detection (fallback)
fn brute_force_collision_detection(
    response_processor: &mut CollisionResponseProcessor,
    metrics: &mut CollisionMetrics,
    config: &CollisionSystemConfig,
    collision_query: &CollisionQuery,
) {
    let fighters: Vec<_> = collision_query.fighters.iter().collect();
    let mut checks_performed = 0;
    
    // Fighter vs Fighter collision
    for i in 0..fighters.len() {
        for j in (i + 1)..fighters.len() {
            if checks_performed >= config.max_checks_per_frame {
                return;
            }
            
            let (entity1, transform1, hitbox_manager1) = &fighters[i];
            let (entity2, transform2, hitbox_manager2) = &fighters[j];
            
            if let (Some(hitboxes1), Some(hitboxes2)) = (
                hitbox_manager1.get_current_hitboxes(),
                hitbox_manager2.get_current_hitboxes(),
            ) {
                let pos1 = Point16::new(
                    transform1.translation.x as i16,
                    transform1.translation.y as i16,
                );
                let pos2 = Point16::new(
                    transform2.translation.x as i16,
                    transform2.translation.y as i16,
                );
                
                // Check hitbox vs hurtbox collisions
                check_fighter_collision(
                    *entity1,
                    *entity2,
                    &hitboxes1,
                    &hitboxes2,
                    pos1,
                    pos2,
                    response_processor,
                    &config.collision_config,
                );
                
                checks_performed += 1;
                metrics.collision_checks += 1;
            }
        }
    }
}

/// Broad-phase collision check using distance threshold
fn broad_phase_check(entry1: &SpatialEntry, entry2: &SpatialEntry, threshold: f32) -> bool {
    let center1 = Point16::new(
        entry1.bounds.origin.x as i16 + entry1.bounds.size.width as i16 / 2,
        entry1.bounds.origin.y as i16 + entry1.bounds.size.height as i16 / 2,
    );
    let center2 = Point16::new(
        entry2.bounds.origin.x as i16 + entry2.bounds.size.width as i16 / 2,
        entry2.bounds.origin.y as i16 + entry2.bounds.size.height as i16 / 2,
    );
    
    let dx = (center2.x - center1.x) as f32;
    let dy = (center2.y - center1.y) as f32;
    let distance_sq = dx * dx + dy * dy;
    
    distance_sq <= threshold * threshold
}

/// Narrow-phase collision check between spatial entries
fn narrow_phase_collision_check(
    entry1: &SpatialEntry,
    entry2: &SpatialEntry,
    collision_query: &CollisionQuery,
    config: &CollisionConfig,
) -> Option<CollisionResult> {
    // Get entity data and perform specific collision checks
    // This is a simplified version - full implementation would handle all collision types
    
    // For now, just check if bounds intersect
    if entry1.bounds.intersects(&entry2.bounds) {
        Some(CollisionResult::new_hit(
            Point8::new(
                (entry1.bounds.origin.x + entry2.bounds.origin.x) / 2,
                (entry1.bounds.origin.y + entry2.bounds.origin.y) / 2,
            ),
            1,
            Point8::new(1, 0),
            0,
        ))
    } else {
        None
    }
}

/// Process a collision between two entities
fn process_collision_pair(
    entity1: Entity,
    entity2: Entity,
    collision_result: CollisionResult,
    response_processor: &mut CollisionResponseProcessor,
    collision_query: &CollisionQuery,
) {
    // Determine collision type and process accordingly
    // This is a simplified version - full implementation would check entity types
    
    response_processor.process_pushbox_collision(entity1, entity2, collision_result);
}

/// Check collision between two fighters
fn check_fighter_collision(
    entity1: Entity,
    entity2: Entity,
    hitboxes1: &FighterHitboxes,
    hitboxes2: &FighterHitboxes,
    pos1: Point16,
    pos2: Point16,
    response_processor: &mut CollisionResponseProcessor,
    config: &CollisionConfig,
) {
    // Check hitbox vs hurtbox collisions
    if let Some(active_hitbox1) = hitboxes1.get_active_hitbox() {
        for hurtbox2 in hitboxes2.get_hurtboxes() {
            let collision_result = SF2CollisionChecker::check_hitbox_overlap(
                active_hitbox1,
                pos1,
                hurtbox2,
                pos2,
                config,
            );
            
            if collision_result.hit {
                response_processor.process_hit_collision(
                    entity1,
                    entity2,
                    active_hitbox1,
                    hurtbox2,
                    collision_result,
                );
            }
        }
    }
    
    if let Some(active_hitbox2) = hitboxes2.get_active_hitbox() {
        for hurtbox1 in hitboxes1.get_hurtboxes() {
            let collision_result = SF2CollisionChecker::check_hitbox_overlap(
                active_hitbox2,
                pos2,
                hurtbox1,
                pos1,
                config,
            );
            
            if collision_result.hit {
                response_processor.process_hit_collision(
                    entity2,
                    entity1,
                    active_hitbox2,
                    hurtbox1,
                    collision_result,
                );
            }
        }
    }
    
    // Check pushbox collisions
    if let (Some(pushbox1), Some(pushbox2)) = (hitboxes1.get_pushbox(), hitboxes2.get_pushbox()) {
        let collision_result = SF2CollisionChecker::check_pushbox_overlap(
            pushbox1,
            pos1,
            pushbox2,
            pos2,
            config,
        );
        
        if collision_result.hit {
            response_processor.process_pushbox_collision(entity1, entity2, collision_result);
        }
    }
}

/// Process collision responses and emit events
fn process_collision_responses(
    responses: Vec<CollisionResponse>,
    hit_events: &mut EventWriter<HitEvent>,
    block_events: &mut EventWriter<BlockEvent>,
    push_events: &mut EventWriter<PushEvent>,
) {
    for response in responses {
        match response.response_type {
            CollisionResponseType::Hit => {
                hit_events.send(HitEvent {
                    attacker: response.entities.0,
                    defender: response.entities.1,
                    damage: response.damage,
                    knockback: response.knockback,
                    hit_position: Point16::new(
                        response.collision_data.collision_point.x as i16,
                        response.collision_data.collision_point.y as i16,
                    ),
                });
            }
            CollisionResponseType::Block => {
                block_events.send(BlockEvent {
                    attacker: response.entities.0,
                    defender: response.entities.1,
                    chip_damage: response.damage,
                    block_position: Point16::new(
                        response.collision_data.collision_point.x as i16,
                        response.collision_data.collision_point.y as i16,
                    ),
                });
            }
            CollisionResponseType::Push => {
                push_events.send(PushEvent {
                    entity1: response.entities.0,
                    entity2: response.entities.1,
                    separation_force: response.knockback,
                });
            }
            _ => {
                // Handle other collision types as needed
            }
        }
    }
}
