//! # SF2 Engine
//! 
//! Core Street Fighter II game engine built with Bevy ECS.
//! 
//! This crate provides the main game systems, components, and resources
//! needed to run a Street Fighter II game.

use bevy::prelude::*;
use sf2_types::{
    FighterStateData,
    FighterId, SpatialGrid, CollisionResponseProcessor, HitboxManager,
};

pub mod components;
pub mod systems;
pub mod resources;
pub mod events;
pub mod states;
pub mod game_logic;
pub mod performance;
pub mod collision_system;
pub mod character_system;
pub mod characters;
pub mod ai;

pub use components::*;
pub use systems::*;
pub use resources::{GameConfig, InputBuffer as GlobalInputBuffer};
pub use states::{GameState, FightState};
pub use events::*;
pub use states::*;
pub use character_system::*;
pub use characters::*;
pub use ai::*;

// QA and testing modules
pub mod qa_testing;
pub use qa_testing::*;

/// Main plugin that sets up the SF2 game engine
pub struct SF2EnginePlugin;

impl Plugin for SF2EnginePlugin {
    fn build(&self, app: &mut App) {
        app
            // Add diagnostics for QA testing
            .add_plugins(bevy::diagnostic::FrameTimeDiagnosticsPlugin)

            // Add game states
            .init_state::<GameState>()
            .init_state::<FightState>()
            
            // Add resources
            .init_resource::<GameConfig>()
            .init_resource::<GlobalInputBuffer>()
            .init_resource::<game_logic::GameLogic>()
            .init_resource::<performance::PerformanceConfig>()
            .init_resource::<performance::PerformanceMetrics>()
            .init_resource::<collision_system::CollisionSystemConfig>()
            .init_resource::<collision_system::CollisionMetrics>()
            .init_resource::<SpatialGrid>()
            .init_resource::<CollisionResponseProcessor>()
            .init_resource::<CharacterRegistry>()

            // QA Testing resources
            .init_resource::<QATestingConfig>()
            .init_resource::<QATestResults>()
            
            // Add events
            .add_event::<HitEvent>()
            .add_event::<SpecialMoveEvent>()
            .add_event::<RoundEndEvent>()
            .add_event::<BlockEvent>()
            .add_event::<PushEvent>()
            .add_event::<CharacterSelectedEvent>()
            .add_event::<CharacterLoadedEvent>()
            
            // Add core systems
            .add_systems(Startup, setup_game)
            .add_systems(
                Update,
                (
                    input_system,
                    special_move_detection_system,
                    movement_system,
                    animation_system,
                    sync_visual_transform_system, // Sync game Position with visual Transform
                    collision_system::collision_detection_system,
                    character_selection_system,
                    character_loading_system,
                    character_behavior_system,
                    character_glitch_system,
                ).run_if(in_state(GameState::InGame))
            )
            .add_systems(
                FixedUpdate,
                (
                    performance::optimized_game_loop_system,
                    physics_system,
                    combat_system,
                ).run_if(in_state(GameState::InGame))
            )

            // Add QA and integration testing systems
            .add_systems(Update, (
                qa_integration_test_system,
                qa_performance_monitor_system,
                qa_state_transition_monitor,
            ).run_if(resource_exists::<QATestingConfig>));
    }
}

/// Initialize the game engine with default settings
pub fn setup_game(
    mut commands: Commands,
    asset_server: Res<AssetServer>,
) {
    info!("SF2 Engine initializing...");

    // Spawn the main camera
    commands.spawn(Camera2d);

    // Create test fighters with new input system and visual components
    setup_test_fighters(&mut commands, &asset_server);

    info!("SF2 Engine initialized successfully");
}

/// Set up test fighters for development
fn setup_test_fighters(commands: &mut Commands, asset_server: &Res<AssetServer>) {
    use sf2_types::{InputConfig, FbDirection, InputDirection, ButtonFlags, InputState};

    // Load input configuration from environment variables
    let input_config = InputConfig::from_env();

    if input_config.debug_input_display {
        input_config.print_config();
    }

    // Player 1 (Left side) - Ryu
    let ryu_entity = commands.spawn((
        Fighter {
            fighter_id: FighterId::Ryu,
            player_number: 1,
        },
        Position {
            x: sf2_types::Fixed16_16::from_f32(100.0),
            y: sf2_types::Fixed16_16::from_f32(200.0),
        },
        Velocity {
            x: sf2_types::Fixed16_16::ZERO,
            y: sf2_types::Fixed16_16::ZERO,
        },
        Health {
            current: 100,
            maximum: 100,
        },
        PlayerInput {
            player_number: 1,
            current_input: InputState {
                direction: InputDirection::Neutral,
                buttons: ButtonFlags::NONE,
                frame: 0,
            },
            previous_input: InputState {
                direction: InputDirection::Neutral,
                buttons: ButtonFlags::NONE,
                frame: 0,
            },
            facing_direction: FbDirection::FacingRight,
        },
        InputBufferComponent {
            buffer: sf2_types::FrameInputBuffer::new(),
            special_move_detector: sf2_types::SpecialMoveDetector::new(),
            c99_state: sf2_types::C99InputState::new(),
            config: input_config.clone(),
        },
        components::FighterState {
            stance: Stance::Standing,
            facing: components::Direction::Right,
            airborne: components::AirborneState::OnGround,
        },
        FighterStateData::new(),
    )).id();

    // Add remaining components to Ryu
    commands.entity(ryu_entity)
        .insert(CollisionBox {
            width: 32,
            height: 64,
            offset_x: -16,
            offset_y: -64,
        })
        .insert(AnimationState {
            current_animation: "idle".to_string(),
            frame: 0,
            timer: 0.0,
        })
        .insert(HitboxManager::new(sf2_types::FighterId::Ryu))
        .insert(Sprite {
            color: Color::srgb(1.0, 0.2, 0.2), // Red tint for Ryu
            custom_size: Some(Vec2::new(64.0, 96.0)), // 64x96 pixel sprite
            ..default()
        })
        .insert(Transform::from_translation(Vec3::new(100.0, 200.0, 0.0)));

    // Player 2 (Right side) - Ken
    let ken_entity = commands.spawn((
        Fighter {
            fighter_id: FighterId::Ken,
            player_number: 2,
        },
        Position {
            x: sf2_types::Fixed16_16::from_f32(300.0),
            y: sf2_types::Fixed16_16::from_f32(200.0),
        },
        Velocity {
            x: sf2_types::Fixed16_16::ZERO,
            y: sf2_types::Fixed16_16::ZERO,
        },
        Health {
            current: 100,
            maximum: 100,
        },
        PlayerInput {
            player_number: 2,
            current_input: InputState {
                direction: InputDirection::Neutral,
                buttons: ButtonFlags::NONE,
                frame: 0,
            },
            previous_input: InputState {
                direction: InputDirection::Neutral,
                buttons: ButtonFlags::NONE,
                frame: 0,
            },
            facing_direction: FbDirection::FacingLeft,
        },
        InputBufferComponent {
            buffer: sf2_types::FrameInputBuffer::new(),
            special_move_detector: sf2_types::SpecialMoveDetector::new(),
            c99_state: sf2_types::C99InputState::new(),
            config: input_config,
        },
        components::FighterState {
            stance: Stance::Standing,
            facing: components::Direction::Left,
            airborne: components::AirborneState::OnGround,
        },
        FighterStateData::new(),
    )).id();

    // Add remaining components to Ken
    commands.entity(ken_entity)
        .insert(CollisionBox {
            width: 32,
            height: 64,
            offset_x: -16,
            offset_y: -64,
        })
        .insert(AnimationState {
            current_animation: "idle".to_string(),
            frame: 0,
            timer: 0.0,
        })
        .insert(HitboxManager::new(sf2_types::FighterId::Ken))
        .insert(Sprite {
            color: Color::srgb(0.2, 0.2, 1.0), // Blue tint for Ken
            custom_size: Some(Vec2::new(64.0, 96.0)), // 64x96 pixel sprite
            ..default()
        })
        .insert(Transform::from_translation(Vec3::new(400.0, 200.0, 0.0)));

    info!("Test fighters spawned with enhanced input system");
}

/// System to synchronize game logic Position with visual Transform for rendering
pub fn sync_visual_transform_system(
    mut query: Query<(&Position, &mut Transform), With<Fighter>>,
) {
    for (position, mut transform) in query.iter_mut() {
        // Convert game logic position to visual transform
        transform.translation.x = position.x.to_f32();
        transform.translation.y = position.y.to_f32();
        // Keep Z at 0.0 for 2D rendering
        transform.translation.z = 0.0;
    }
}
