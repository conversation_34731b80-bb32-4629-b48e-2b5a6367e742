//! # Game Logic Core
//! 
//! Memory-safe implementation of core game logic functions matching the original
//! C99 engine's behavior while leveraging Rust's ownership system for safety.

use bevy::prelude::*;
use sf2_types::{
    FighterStateData, FighterState as SF2FighterState, RoundMode, GameState as SF2GameState, SubMode, AnimMode, AudioMode, ExtendedMode,
    FrameTimer, FrameBudgetManager, CountdownTimer, FighterStateTransition,
    Fixed16_16,
};
use crate::components::*;
use crate::events::*;

/// Game logic resource for managing global game state
#[derive(Resource, Debug)]
pub struct GameLogic {
    /// Global game state
    pub game_state: SF2GameState,
    
    /// Frame timer for precise timing
    pub frame_timer: FrameTimer,
    
    /// Frame budget manager
    pub budget_manager: FrameBudgetManager,
    
    /// Round timer
    pub round_timer: CountdownTimer,
    
    /// Game flags
    pub flags: GameLogicFlags,
}

/// Game logic flags
#[derive(Debu<PERSON>, <PERSON><PERSON>, Default)]
pub struct GameLogicFlags {
    /// New challenger joined
    pub new_challenger: bool,
    
    /// Round complete
    pub round_complete: bool,
    
    /// Game paused
    pub paused: bool,
    
    /// Debug mode active
    pub debug_mode: bool,
    
    /// Collision detection enabled
    pub collision_enabled: bool,
    
    /// Physics enabled
    pub physics_enabled: bool,
}

impl GameLogic {
    /// Create new game logic manager
    pub fn new() -> Self {
        Self {
            game_state: SF2GameState::new(),
            frame_timer: FrameTimer::new(),
            budget_manager: FrameBudgetManager::new(1000), // Default budget
            round_timer: CountdownTimer::new(99 * 60, true), // 99 seconds
            flags: GameLogicFlags::default(),
        }
    }
    
    /// Initialize new game
    pub fn new_game(&mut self) {
        self.game_state.reset();
        self.round_timer.reset();
        self.flags = GameLogicFlags::default();
        self.flags.collision_enabled = true;
        self.flags.physics_enabled = true;
        
        info!("New game initialized");
    }
    
    /// Check if new challenger joined (matching check_if_new_player)
    pub fn check_new_challenger(&mut self) -> bool {
        if self.flags.new_challenger {
            self.game_state.round_mode = RoundMode::Final;
            self.game_state.sub_mode = SubMode::State0;
            self.game_state.anim_mode = AnimMode::Idle;
            self.game_state.audio_mode = AudioMode::Silent;
            self.game_state.extended_mode = ExtendedMode::Normal;
            self.game_state.flags.wait_mode = false;
            self.flags.new_challenger = false;
            true
        } else {
            false
        }
    }
    
    /// Check round result and timeout
    pub fn check_round_result(&mut self) -> bool {
        if self.round_timer.is_expired() {
            self.flags.round_complete = true;
            info!("Round timeout - round complete");
            true
        } else {
            false
        }
    }
    
    /// Advance game logic by one frame
    pub fn advance_frame(&mut self) {
        self.frame_timer.advance_frame();
        self.game_state.advance_frame();
        self.round_timer.update();
        self.budget_manager.reset_frame();
    }
}

impl Default for GameLogic {
    fn default() -> Self {
        Self::new()
    }
}

/// Main fight tick system (matching fighttick)
pub fn fight_tick_system(
    mut game_logic: ResMut<GameLogic>,
    mut fighter_query: Query<(
        Entity,
        &mut FighterStateData,
        &mut Position,
        &mut Velocity,
        &mut Health,
        &InputBufferComponent,
    ), With<Fighter>>,
    mut special_move_events: EventWriter<SpecialMoveEvent>,
    mut hit_events: EventWriter<HitEvent>,
) {
    // Skip if game is paused
    if game_logic.flags.paused {
        return;
    }
    
    // Check for new challenger
    if game_logic.check_new_challenger() {
        return;
    }
    
    // Check round timeout
    game_logic.check_round_result();
    
    // Process all fighter actions if round is active
    if !game_logic.flags.round_complete {
        process_fighter_actions(
            &mut game_logic,
            &mut fighter_query,
            &mut special_move_events,
            &mut hit_events,
        );
        
        // Process physics if enabled
        if game_logic.flags.physics_enabled {
            apply_physics_rules(&mut fighter_query);
        }
        
        // Process collisions if enabled
        if game_logic.flags.collision_enabled {
            process_collisions(&mut fighter_query, &mut hit_events);
        }
    }
    
    // Advance frame
    game_logic.advance_frame();
}

/// Process all fighter actions (matching proc_all_actions)
fn process_fighter_actions(
    game_logic: &mut ResMut<GameLogic>,
    fighter_query: &mut Query<(
        Entity,
        &mut FighterStateData,
        &mut Position,
        &mut Velocity,
        &mut Health,
        &InputBufferComponent,
    ), With<Fighter>>,
    special_move_events: &mut EventWriter<SpecialMoveEvent>,
    hit_events: &mut EventWriter<HitEvent>,
) {
    // Process each fighter
    for (entity, mut fighter_state, mut position, mut velocity, health, input_buffer) in fighter_query.iter_mut() {
        // Advance fighter frame
        fighter_state.advance_frame();
        
        // Execute queued actions
        if let Some(transition_result) = fighter_state.execute_next_action() {
            match transition_result {
                FighterStateTransition::Success => {
                    trace!("Fighter {:?} executed action successfully", entity);
                }
                FighterStateTransition::Blocked(reason) => {
                    trace!("Fighter {:?} action blocked: {}", entity, reason);
                }
                FighterStateTransition::Invalid(reason) => {
                    warn!("Fighter {:?} invalid action: {}", entity, reason);
                }
                FighterStateTransition::Delayed(frames) => {
                    trace!("Fighter {:?} action delayed {} frames", entity, frames);
                }
            }
        }
        
        // Process state-specific logic
        match fighter_state.state {
            SF2FighterState::Normal => {
                process_normal_state(&mut fighter_state, &mut position, &mut velocity, input_buffer);
            }
            SF2FighterState::Crouch => {
                process_crouch_state(&mut fighter_state, &mut position, &mut velocity, input_buffer);
            }
            SF2FighterState::Jumping => {
                process_jumping_state(&mut fighter_state, &mut position, &mut velocity);
            }
            SF2FighterState::Attacking => {
                process_attacking_state(&mut fighter_state, &mut position, &mut velocity, hit_events, entity);
            }
            SF2FighterState::InPowerMove => {
                process_power_move_state(&mut fighter_state, &mut position, &mut velocity, special_move_events, entity);
            }
            SF2FighterState::Reel => {
                process_reel_state(&mut fighter_state, &mut position, &mut velocity);
            }
            SF2FighterState::StandBlock => {
                process_block_state(&mut fighter_state, &mut position, &mut velocity);
            }
            SF2FighterState::TurnAround => {
                process_turn_around_state(&mut fighter_state);
            }
            SF2FighterState::Victory | SF2FighterState::Loss => {
                process_end_state(&mut fighter_state);
            }
            SF2FighterState::Tumble => {
                process_tumble_state(&mut fighter_state, &mut position, &mut velocity);
            }
        }
        
        // Update position based on velocity
        position.x += velocity.x;
        position.y += velocity.y;
        
        // Apply gravity if airborne
        if !fighter_state.on_ground {
            velocity.y += Fixed16_16::from_f32(-0.5); // Gravity
            
            // Check for landing
            if position.y <= Fixed16_16::ZERO {
                position.y = Fixed16_16::ZERO;
                velocity.y = Fixed16_16::ZERO;
                fighter_state.on_ground = true;
                
                // Transition from jumping to normal
                if fighter_state.state == SF2FighterState::Jumping {
                    let _ = fighter_state.transition_state(SF2FighterState::Normal);
                }
            }
        }
    }
}

/// Apply physics rules (matching ApplyPhysicsRules)
fn apply_physics_rules(
    fighter_query: &mut Query<(
        Entity,
        &mut FighterStateData,
        &mut Position,
        &mut Velocity,
        &mut Health,
        &InputBufferComponent,
    ), With<Fighter>>,
) {
    // Apply friction to grounded fighters
    for (_, fighter_state, _, mut velocity, _, _) in fighter_query.iter_mut() {
        if fighter_state.on_ground {
            // Apply friction
            velocity.x *= Fixed16_16::from_f32(0.9);
            
            // Stop very small movements
            if velocity.x.abs() < Fixed16_16::from_f32(0.01) {
                velocity.x = Fixed16_16::ZERO;
            }
        }
    }
}

/// Process collisions between fighters
fn process_collisions(
    fighter_query: &mut Query<(
        Entity,
        &mut FighterStateData,
        &mut Position,
        &mut Velocity,
        &mut Health,
        &InputBufferComponent,
    ), With<Fighter>>,
    hit_events: &mut EventWriter<HitEvent>,
) {
    // Simple collision detection - this would be expanded with proper hitbox/hurtbox system
    let fighters: Vec<_> = fighter_query.iter().collect();
    
    for i in 0..fighters.len() {
        for j in (i + 1)..fighters.len() {
            let (entity1, _, pos1, _, _, _) = &fighters[i];
            let (entity2, _, pos2, _, _, _) = &fighters[j];
            
            // Simple distance check
            let distance = (pos1.x - pos2.x).abs() + (pos1.y - pos2.y).abs();
            if distance < Fixed16_16::from_f32(64.0) {
                // Fighters are close - could trigger collision events
                trace!("Fighters {:?} and {:?} are in collision range", entity1, entity2);
            }
        }
    }
}

// State processing functions (simplified implementations)
fn process_normal_state(
    _fighter_state: &mut FighterStateData,
    _position: &mut Position,
    _velocity: &mut Velocity,
    _input_buffer: &InputBufferComponent,
) {
    // Process normal state logic
}

fn process_crouch_state(
    _fighter_state: &mut FighterStateData,
    _position: &mut Position,
    _velocity: &mut Velocity,
    _input_buffer: &InputBufferComponent,
) {
    // Process crouch state logic
}

fn process_jumping_state(
    _fighter_state: &mut FighterStateData,
    _position: &mut Position,
    _velocity: &mut Velocity,
) {
    // Process jumping state logic
}

fn process_attacking_state(
    _fighter_state: &mut FighterStateData,
    _position: &mut Position,
    _velocity: &mut Velocity,
    _hit_events: &mut EventWriter<HitEvent>,
    _entity: Entity,
) {
    // Process attacking state logic
}

fn process_power_move_state(
    _fighter_state: &mut FighterStateData,
    _position: &mut Position,
    _velocity: &mut Velocity,
    _special_move_events: &mut EventWriter<SpecialMoveEvent>,
    _entity: Entity,
) {
    // Process power move state logic
}

fn process_reel_state(
    _fighter_state: &mut FighterStateData,
    _position: &mut Position,
    _velocity: &mut Velocity,
) {
    // Process reel state logic
}

fn process_block_state(
    _fighter_state: &mut FighterStateData,
    _position: &mut Position,
    _velocity: &mut Velocity,
) {
    // Process block state logic
}

fn process_turn_around_state(_fighter_state: &mut FighterStateData) {
    // Process turn around state logic
}

fn process_end_state(_fighter_state: &mut FighterStateData) {
    // Process victory/loss state logic
}

fn process_tumble_state(
    _fighter_state: &mut FighterStateData,
    _position: &mut Position,
    _velocity: &mut Velocity,
) {
    // Process tumble state logic
}
