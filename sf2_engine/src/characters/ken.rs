//! # Ken Implementation
//!
//! Ken character implementation with authentic differences from Ryu.
//! Based on original Street Fighter II frame data and behavior patterns.

use sf2_types::{
    FighterId, FighterStateData, Point16, Vect16, Fixed8_8, InputState, InputDirection, ButtonInput,
    character_traits::{
        FighterCharacter, FighterStats, MoveId, FighterAction,
        SpecialMoveResult, FrameData, HitboxData, HurtboxData, BodyPart,
        AttackData, HitResponse, BlockResponse, AIBehaviorPattern,
        CharacterSounds, CharacterEffects, EffectId
    },
    fighter::SpecialMoveId,
};

/// Ken character implementation with differences from Ryu
pub struct Ken<PERSON>haracter;

impl FighterCharacter for KenCharacter {
    fn fighter_id(&self) -> FighterId {
        FighterId::Ken
    }
    
    fn name(&self) -> &'static str {
        "<PERSON>"
    }
    
    fn base_stats(&self) -> FighterStats {
        FighterStats {
            health: 16000,
            stun_threshold: 7200,
            walk_speed: Fixed8_8::from_i16(125), // 5 units faster than Ryu
            jump_speed: Fixed8_8::from_i16(410), // 10 units higher than Ryu
            gravity: Fixed8_8::from_i16(32),
            throw_range: 20,
            max_projectiles: 1,
        }
    }
    
    fn process_input(&self, input: &InputState, state: &mut FighterStateData) -> Vec<FighterAction> {
        let mut actions = Vec::new();
        
        // Handle special move inputs first
        if let Some(special_action) = self.check_special_moves(input, state) {
            actions.push(special_action);
            return actions;
        }
        
        // Handle normal moves (same as Ryu)
        if input.buttons.has_button(ButtonInput::LightPunch) {
            actions.push(FighterAction::ExecuteMove(MoveId::StandingLightPunch));
        }

        if input.buttons.has_button(ButtonInput::MediumPunch) {
            actions.push(FighterAction::ExecuteMove(MoveId::StandingMediumPunch));
        }

        if input.buttons.has_button(ButtonInput::HeavyPunch) {
            actions.push(FighterAction::ExecuteMove(MoveId::StandingHeavyPunch));
        }

        // Handle movement (Ken is slightly faster)
        match input.direction {
            InputDirection::Right => {
                if state.on_ground {
                    actions.push(FighterAction::SetVelocity(Fixed8_8::from_i16(125), Fixed8_8::ZERO));
                }
            }
            InputDirection::Left => {
                if state.on_ground {
                    actions.push(FighterAction::SetVelocity(Fixed8_8::from_i16(-85), Fixed8_8::ZERO));
                }
            }
            InputDirection::Down => {
                if state.on_ground {
                    actions.push(FighterAction::StateTransition(sf2_types::FighterState::Crouch));
                }
            }
            InputDirection::Up => {
                if state.on_ground {
                    actions.push(FighterAction::StateTransition(sf2_types::FighterState::Jumping));
                    actions.push(FighterAction::SetVelocity(Fixed8_8::ZERO, Fixed8_8::from_i16(410)));
                }
            }
            _ => {
                if state.on_ground {
                    actions.push(FighterAction::SetVelocity(Fixed8_8::ZERO, Fixed8_8::ZERO));
                }
            }
        }
        
        actions
    }
    
    fn update_physics(&self, state: &mut FighterStateData, position: &mut Point16, velocity: &mut Vect16) {
        // Apply gravity when airborne (same as Ryu)
        if !state.on_ground {
            velocity.y = velocity.y - Fixed8_8::from_i16(32);
        }

        // Check ground collision
        if position.y <= 0 && velocity.y <= Fixed8_8::ZERO {
            position.y = 0;
            velocity.y = Fixed8_8::ZERO;
            state.on_ground = true;

            if matches!(state.state, sf2_types::FighterState::Jumping) {
                state.transition_state(sf2_types::FighterState::Normal);
            }
        }

        // Apply friction when on ground (Ken has slightly less friction)
        if state.on_ground && velocity.x != Fixed8_8::ZERO {
            let friction = Fixed8_8::from_i16(7); // 1 less than Ryu
            if velocity.x > Fixed8_8::ZERO {
                velocity.x = (velocity.x - friction).max(Fixed8_8::ZERO);
            } else {
                velocity.x = (velocity.x + friction).min(Fixed8_8::ZERO);
            }
        }
    }
    
    fn execute_special_move(&self, move_id: SpecialMoveId, state: &mut FighterStateData) -> SpecialMoveResult {
        match move_id {
            SpecialMoveId::Hadoken => self.execute_hadoken(state),
            SpecialMoveId::Shoryuken => self.execute_shoryuken(state),
            SpecialMoveId::TatsumakiSenpukyaku => self.execute_tatsumaki(state),
            _ => SpecialMoveResult {
                success: false,
                actions: vec![],
                meter_cost: 0,
                recovery_frames: 0,
            },
        }
    }
    
    fn get_frame_data(&self, move_id: MoveId) -> FrameData {
        match move_id {
            // Ken's normal moves have same frame data as Ryu
            MoveId::StandingLightPunch => FrameData {
                startup_frames: 3,
                active_frames: 2,
                recovery_frames: 6,
                block_stun: 8,
                hit_stun: 12,
                damage: 200,
                stun_damage: 100,
                knockback_x: 100,
                knockback_y: 0,
            },
            MoveId::StandingMediumPunch => FrameData {
                startup_frames: 4,
                active_frames: 3,
                recovery_frames: 8,
                block_stun: 12,
                hit_stun: 16,
                damage: 600,
                stun_damage: 300,
                knockback_x: 150,
                knockback_y: 0,
            },
            MoveId::StandingHeavyPunch => FrameData {
                startup_frames: 6,
                active_frames: 4,
                recovery_frames: 12,
                block_stun: 16,
                hit_stun: 20,
                damage: 1000,
                stun_damage: 500,
                knockback_x: 200,
                knockback_y: 0,
            },
            _ => FrameData {
                startup_frames: 0,
                active_frames: 0,
                recovery_frames: 0,
                block_stun: 0,
                hit_stun: 0,
                damage: 0,
                stun_damage: 0,
                knockback_x: 0,
                knockback_y: 0,
            },
        }
    }
    
    fn get_hitboxes(&self, state: &FighterStateData) -> Vec<HitboxData> {
        vec![]
    }
    
    fn get_hurtboxes(&self, state: &FighterStateData) -> Vec<HurtboxData> {
        vec![
            HurtboxData {
                rect: sf2_types::Rect8::new(-20, -60, 40, 60),
                vulnerability: 1.0,
                body_part: BodyPart::Body,
            }
        ]
    }
    
    fn on_hit(&self, attack: &AttackData, state: &mut FighterStateData) -> HitResponse {
        HitResponse {
            damage_taken: attack.damage,
            stun_taken: attack.stun,
            knockback: attack.knockback,
            new_state: sf2_types::FighterState::Reel,
            hit_stun_frames: (attack.damage / 100) + 8,
            effects: vec![EffectId::HitSpark],
        }
    }
    
    fn on_block(&self, attack: &AttackData, state: &mut FighterStateData) -> BlockResponse {
        BlockResponse {
            chip_damage: attack.damage / 8,
            block_stun_frames: (attack.damage / 200) + 4,
            pushback: Fixed8_8::from_i16(50),
            effects: vec![EffectId::BlockSpark],
        }
    }
    
    fn get_ai_behavior(&self, difficulty: u8) -> AIBehaviorPattern {
        let base_aggression = 0.7; // More aggressive than Ryu
        let base_defense = 0.6;    // Less defensive than Ryu
        
        AIBehaviorPattern {
            aggression: base_aggression + (difficulty as f32 * 0.05),
            defense: base_defense + (difficulty as f32 * 0.03),
            special_move_frequency: 0.5 + (difficulty as f32 * 0.1), // Uses specials more often
            jump_frequency: 0.4, // Jumps more than Ryu
            preferred_distance: Fixed8_8::from_i16(130), // Prefers closer range than Ryu
            reaction_time: 18 - (difficulty as u16 * 2), // Slightly faster reactions
        }
    }
    
    fn get_sound_effects(&self) -> CharacterSounds {
        CharacterSounds {
            voice_clips: vec![0x6D, 0x6E, 0x6F], // Different voice clips from Ryu
            attack_sounds: vec![0x13, 0x14, 0x15],
            special_move_sounds: vec![0x6D, 0x6E, 0x6F],
            hit_sounds: vec![0x22, 0x23],
            victory_sound: 0x71,
        }
    }
    
    fn get_visual_effects(&self) -> CharacterEffects {
        CharacterEffects {
            hit_sparks: vec![EffectId::HitSpark],
            special_effects: vec![EffectId::Fire],
            victory_effects: vec![EffectId::Custom(101)],
        }
    }
}

impl KenCharacter {
    /// Check for special move inputs
    fn check_special_moves(&self, input: &InputState, state: &FighterStateData) -> Option<FighterAction> {
        // Same input patterns as Ryu
        if self.check_hadoken_input(input) {
            return Some(FighterAction::ExecuteMove(MoveId::Special(SpecialMoveId::Hadoken)));
        }
        
        if self.check_shoryuken_input(input) {
            return Some(FighterAction::ExecuteMove(MoveId::Special(SpecialMoveId::Shoryuken)));
        }
        
        if self.check_tatsumaki_input(input) {
            return Some(FighterAction::ExecuteMove(MoveId::Special(SpecialMoveId::TatsumakiSenpukyaku)));
        }
        
        None
    }
    
    fn check_hadoken_input(&self, input: &InputState) -> bool {
        input.buttons.any_punch()
    }

    fn check_shoryuken_input(&self, input: &InputState) -> bool {
        input.buttons.any_punch()
    }

    fn check_tatsumaki_input(&self, input: &InputState) -> bool {
        input.buttons.any_kick()
    }
    
    /// Execute Ken's Hadoken (faster recovery than Ryu)
    fn execute_hadoken(&self, state: &mut FighterStateData) -> SpecialMoveResult {
        SpecialMoveResult {
            success: true,
            actions: vec![
                FighterAction::SetAnimation(200),
                FighterAction::PlaySound(0x6D), // Ken's voice
            ],
            meter_cost: 0,
            recovery_frames: 25, // 5 frames faster than Ryu
        }
    }
    
    /// Execute Ken's Shoryuken (different arc, multi-hit)
    fn execute_shoryuken(&self, state: &mut FighterStateData) -> SpecialMoveResult {
        SpecialMoveResult {
            success: true,
            actions: vec![
                FighterAction::SetAnimation(210),
                FighterAction::PlaySound(0x6E),
                // Ken's Shoryuken has different velocity profile
                FighterAction::SetVelocity(Fixed8_8::from_i16(80), Fixed8_8::from_i16(520)),
            ],
            meter_cost: 0,
            recovery_frames: 42, // Slightly longer recovery due to multi-hit
        }
    }
    
    /// Execute Ken's Tatsumaki (multi-hit, different properties)
    fn execute_tatsumaki(&self, state: &mut FighterStateData) -> SpecialMoveResult {
        SpecialMoveResult {
            success: true,
            actions: vec![
                FighterAction::SetAnimation(220),
                FighterAction::PlaySound(0x6F),
                // Ken's Tatsumaki moves forward more and hits multiple times
                FighterAction::SetVelocity(Fixed8_8::from_i16(250), Fixed8_8::from_i16(280)),
            ],
            meter_cost: 0,
            recovery_frames: 38, // Slightly longer due to multi-hit
        }
    }
}
