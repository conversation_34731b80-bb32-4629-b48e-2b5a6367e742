//! # Quality Assurance Testing Module
//! 
//! Comprehensive integration testing, performance profiling, and compatibility testing
//! for Phase 8.3 Quality Assurance.

use bevy::prelude::*;
use bevy::diagnostic::{DiagnosticsStore, FrameTimeDiagnosticsPlugin};
use bevy::window::PrimaryWindow;
use std::time::{Duration, Instant};
use std::collections::VecDeque;
use log::{info, warn, debug};

use crate::states::{GameState, FightState};
use crate::components::*;

use sf2_assets::{RomData, AssetExtractionProgress, ExtractedSprites, ExtractedAudio};

/// QA Testing configuration resource
#[derive(Resource, Debug)]
pub struct QATestingConfig {
    /// Enable integration testing
    pub integration_testing_enabled: bool,
    /// Enable performance profiling
    pub performance_profiling_enabled: bool,
    /// Enable live game testing
    pub live_testing_enabled: bool,
    /// Test duration in seconds
    pub test_duration_seconds: f32,
    /// Performance sampling interval
    pub performance_sample_interval: Duration,
    /// Memory usage tracking
    pub track_memory_usage: bool,
    /// Frame rate target for testing
    pub target_fps: f32,
}

impl Default for QATestingConfig {
    fn default() -> Self {
        Self {
            integration_testing_enabled: true,
            performance_profiling_enabled: true,
            live_testing_enabled: true,
            test_duration_seconds: 300.0, // 5 minutes
            performance_sample_interval: Duration::from_millis(100),
            track_memory_usage: true,
            target_fps: 60.0,
        }
    }
}

/// QA Test results and metrics
#[derive(Resource, Debug)]
pub struct QATestResults {
    /// Test start time
    pub test_start_time: Instant,
    /// Integration test results
    pub integration_tests: IntegrationTestResults,
    /// Performance metrics
    pub performance_metrics: PerformanceTestResults,
    /// Live testing results
    pub live_test_results: LiveTestResults,
    /// Overall test status
    pub test_status: QATestStatus,
}

#[derive(Debug)]
pub struct IntegrationTestResults {
    /// ROM loading test result
    pub rom_loading_passed: bool,
    /// Asset extraction test result
    pub asset_extraction_passed: bool,
    /// Game initialization test result
    pub game_init_passed: bool,
    /// State transition test result
    pub state_transitions_passed: bool,
    /// Character system test result
    pub character_system_passed: bool,
    /// Input system test result
    pub input_system_passed: bool,
    /// Collision system test result
    pub collision_system_passed: bool,
    /// Test failure messages
    pub failure_messages: Vec<String>,
}

#[derive(Debug)]
pub struct PerformanceTestResults {
    /// Frame rate samples
    pub fps_samples: VecDeque<f32>,
    /// Frame time samples (milliseconds)
    pub frame_time_samples: VecDeque<f32>,
    /// Memory usage samples (MB)
    pub memory_samples: VecDeque<f32>,
    /// Average FPS over test period
    pub average_fps: f32,
    /// Minimum FPS recorded
    pub min_fps: f32,
    /// Maximum FPS recorded
    pub max_fps: f32,
    /// Frame drops count
    pub frame_drops: u32,
    /// Performance issues detected
    pub performance_issues: Vec<String>,
}

#[derive(Debug)]
pub struct LiveTestResults {
    /// Total runtime in seconds
    pub runtime_seconds: f32,
    /// Game state changes observed
    pub state_changes: Vec<(GameState, Instant)>,
    /// Errors encountered during live testing
    pub runtime_errors: Vec<String>,
    /// Memory leaks detected
    pub memory_leaks_detected: bool,
    /// Stability rating (0.0 - 1.0)
    pub stability_rating: f32,

    // Full Game Flow Test Results
    /// Full game flow test start time
    pub full_game_flow_test_started: Option<Instant>,
    /// Full game flow test completion time
    pub full_game_flow_completion_time: Option<Instant>,
    /// Full game flow test completed successfully
    pub full_game_flow_test_completed: bool,

    // Pipeline Test Results
    /// ROM to Asset Extraction pipeline passed
    pub rom_to_asset_pipeline_passed: bool,
    /// Character to Visual Rendering pipeline passed
    pub character_visual_pipeline_passed: bool,
    /// Character visual pipeline completion time
    pub character_visual_pipeline_time: Option<Instant>,
    /// Input to Game Logic pipeline passed
    pub input_game_logic_pipeline_passed: bool,
    /// Game State Management pipeline passed
    pub game_state_pipeline_passed: bool,
    /// Game state pipeline completion time
    pub game_state_pipeline_time: Option<Instant>,
    /// Performance and Stability pipeline passed
    pub performance_stability_pipeline_passed: bool,
}

#[derive(Debug, Clone, Copy, PartialEq)]
pub enum QATestStatus {
    NotStarted,
    Running,
    Completed,
    Failed,
}

impl Default for QATestResults {
    fn default() -> Self {
        Self {
            test_start_time: Instant::now(),
            integration_tests: IntegrationTestResults::default(),
            performance_metrics: PerformanceTestResults::default(),
            live_test_results: LiveTestResults::default(),
            test_status: QATestStatus::NotStarted,
        }
    }
}

impl Default for IntegrationTestResults {
    fn default() -> Self {
        Self {
            rom_loading_passed: false,
            asset_extraction_passed: false,
            game_init_passed: false,
            state_transitions_passed: false,
            character_system_passed: false,
            input_system_passed: false,
            collision_system_passed: false,
            failure_messages: Vec::new(),
        }
    }
}

impl Default for PerformanceTestResults {
    fn default() -> Self {
        Self {
            fps_samples: VecDeque::with_capacity(1000),
            frame_time_samples: VecDeque::with_capacity(1000),
            memory_samples: VecDeque::with_capacity(1000),
            average_fps: 0.0,
            min_fps: f32::MAX,
            max_fps: 0.0,
            frame_drops: 0,
            performance_issues: Vec::new(),
        }
    }
}

impl Default for LiveTestResults {
    fn default() -> Self {
        Self {
            runtime_seconds: 0.0,
            state_changes: Vec::new(),
            runtime_errors: Vec::new(),
            memory_leaks_detected: false,
            stability_rating: 1.0,

            // Full Game Flow Test defaults
            full_game_flow_test_started: None,
            full_game_flow_completion_time: None,
            full_game_flow_test_completed: false,

            // Pipeline Test defaults
            rom_to_asset_pipeline_passed: false,
            character_visual_pipeline_passed: false,
            character_visual_pipeline_time: None,
            input_game_logic_pipeline_passed: false,
            game_state_pipeline_passed: false,
            game_state_pipeline_time: None,
            performance_stability_pipeline_passed: false,
        }
    }
}

/// Main QA integration testing system
pub fn qa_integration_test_system(
    mut qa_results: ResMut<QATestResults>,
    qa_config: Res<QATestingConfig>,
    rom_data: Res<RomData>,
    extraction_progress: Res<AssetExtractionProgress>,
    extracted_sprites: Res<ExtractedSprites>,
    extracted_audio: Res<ExtractedAudio>,
    game_state: Res<State<GameState>>,
    fight_state: Res<State<FightState>>,
    fighter_query: Query<Entity, With<Fighter>>,
    window_query: Query<&Window, With<PrimaryWindow>>,
) {
    if !qa_config.integration_testing_enabled {
        return;
    }

    // Start integration tests if not started
    if qa_results.test_status == QATestStatus::NotStarted {
        info!("=== Starting QA Integration Tests ===");
        qa_results.test_status = QATestStatus::Running;
        qa_results.test_start_time = Instant::now();
    }

    // Test ROM loading
    test_rom_loading(&mut qa_results.integration_tests, &rom_data);
    
    // Test asset extraction
    test_asset_extraction(&mut qa_results.integration_tests, &extraction_progress, &extracted_sprites, &extracted_audio);
    
    // Test game initialization
    test_game_initialization(&mut qa_results.integration_tests, &window_query);
    
    // Test state transitions
    test_state_transitions(&mut qa_results.integration_tests, &game_state, &fight_state);
    
    // Test character system
    test_character_system(&mut qa_results.integration_tests, &fighter_query);
    
    // Log integration test results
    if qa_results.integration_tests.all_tests_passed() {
        info!("✅ All integration tests passed");
    } else {
        warn!("❌ Some integration tests failed: {:?}", qa_results.integration_tests.failure_messages);
    }
}

/// Performance monitoring system for QA
pub fn qa_performance_monitor_system(
    mut qa_results: ResMut<QATestResults>,
    qa_config: Res<QATestingConfig>,
    diagnostics: Res<DiagnosticsStore>,
    _time: Res<Time>,
) {
    if !qa_config.performance_profiling_enabled {
        return;
    }

    let elapsed = qa_results.test_start_time.elapsed();
    
    // Sample performance metrics
    if let Some(fps_diagnostic) = diagnostics.get(&FrameTimeDiagnosticsPlugin::FPS) {
        if let Some(fps) = fps_diagnostic.smoothed() {
            sample_performance_metrics(&mut qa_results.performance_metrics, fps as f32, &qa_config);
        }
    }
    
    // Check for performance issues
    check_performance_issues(&mut qa_results.performance_metrics, &qa_config);
    
    // Update live test results
    qa_results.live_test_results.runtime_seconds = elapsed.as_secs_f32();
    
    // Log performance summary every 30 seconds
    if elapsed.as_secs() % 30 == 0 && elapsed.as_secs() > 0 {
        log_performance_summary(&qa_results.performance_metrics);
    }
}

/// State transition monitoring system
pub fn qa_state_transition_monitor(
    mut qa_results: ResMut<QATestResults>,
    game_state: Res<State<GameState>>,
    _fight_state: Res<State<FightState>>,
) {
    // Track state changes
    let current_state = *game_state.get();

    // Check if state changed
    let last_state = qa_results.live_test_results.state_changes.last().map(|(state, _)| *state);

    if let Some(last) = last_state {
        if last != current_state {
            qa_results.live_test_results.state_changes.push((current_state, Instant::now()));
            debug!("State transition detected: {:?} -> {:?}", last, current_state);
        }
    } else {
        // First state recorded
        qa_results.live_test_results.state_changes.push((current_state, Instant::now()));
    }
}

// Helper functions for integration testing
fn test_rom_loading(results: &mut IntegrationTestResults, rom_data: &RomData) {
    if rom_data.loading_error.is_none() && rom_data.loaded_roms.is_some() {
        if let Some(loaded_roms) = &rom_data.loaded_roms {
            if !loaded_roms.graphics_rom.is_empty() && !loaded_roms.code_rom.is_empty() {
                results.rom_loading_passed = true;
                debug!("✅ ROM loading test passed - graphics: {} bytes, code: {} bytes",
                       loaded_roms.graphics_rom.len(), loaded_roms.code_rom.len());
                return;
            }
        }
    }

    results.rom_loading_passed = false;
    let error_msg = format!("ROM loading failed: {:?}", rom_data.loading_error);
    results.failure_messages.push(error_msg);
    debug!("❌ ROM loading test failed");
}

fn test_asset_extraction(
    results: &mut IntegrationTestResults,
    progress: &AssetExtractionProgress,
    sprites: &ExtractedSprites,
    audio: &ExtractedAudio,
) {
    let sprites_extracted = !sprites.sprites.is_empty();
    let audio_extracted = !audio.samples.is_empty();
    
    if sprites_extracted && audio_extracted {
        results.asset_extraction_passed = true;
        debug!("✅ Asset extraction test passed");
    } else {
        results.asset_extraction_passed = false;
        let error_msg = format!("Asset extraction incomplete - sprites: {}, audio: {}", 
                               sprites_extracted, audio_extracted);
        results.failure_messages.push(error_msg);
        debug!("❌ Asset extraction test failed");
    }
}

fn test_game_initialization(results: &mut IntegrationTestResults, window_query: &Query<&Window, With<PrimaryWindow>>) {
    if let Ok(window) = window_query.get_single() {
        if window.resolution.width() > 0.0 && window.resolution.height() > 0.0 {
            results.game_init_passed = true;
            debug!("✅ Game initialization test passed");
        } else {
            results.game_init_passed = false;
            results.failure_messages.push("Window has invalid resolution".to_string());
            debug!("❌ Game initialization test failed - invalid window resolution");
        }
    } else {
        results.game_init_passed = false;
        results.failure_messages.push("Primary window not found".to_string());
        debug!("❌ Game initialization test failed - no primary window");
    }
}

fn test_state_transitions(
    results: &mut IntegrationTestResults,
    game_state: &State<GameState>,
    fight_state: &State<FightState>,
) {
    // Basic state validation
    let valid_game_state = matches!(game_state.get(), 
        GameState::Loading | GameState::MainMenu | GameState::CharacterSelect | 
        GameState::InGame | GameState::Paused | GameState::GameOver | GameState::Credits);
    
    let valid_fight_state = matches!(fight_state.get(),
        FightState::PreFight | FightState::Fighting | FightState::RoundEnd | FightState::Victory);
    
    if valid_game_state && valid_fight_state {
        results.state_transitions_passed = true;
        debug!("✅ State transitions test passed");
    } else {
        results.state_transitions_passed = false;
        let error_msg = format!("Invalid state combination - game: {:?}, fight: {:?}", 
                               game_state.get(), fight_state.get());
        results.failure_messages.push(error_msg);
        debug!("❌ State transitions test failed");
    }
}

fn test_character_system(results: &mut IntegrationTestResults, fighter_query: &Query<Entity, With<Fighter>>) {
    let fighter_count = fighter_query.iter().count();

    if fighter_count >= 2 {
        results.character_system_passed = true;
        debug!("✅ Character system test passed - {} fighters spawned", fighter_count);
    } else {
        results.character_system_passed = false;
        let error_msg = format!("Insufficient fighters spawned: {}", fighter_count);
        results.failure_messages.push(error_msg);
        debug!("❌ Character system test failed - only {} fighters", fighter_count);
    }
}

/// Comprehensive full game flow testing system
pub fn qa_full_game_flow_test_system(
    qa_config: Res<QATestingConfig>,
    mut qa_results: ResMut<QATestResults>,
    rom_data: Option<Res<RomData>>,
    extraction_progress: Option<Res<AssetExtractionProgress>>,
    extracted_sprites: Option<Res<ExtractedSprites>>,
    extracted_audio: Option<Res<ExtractedAudio>>,
    fighter_query: Query<(Entity, &Fighter, &Position, &Health, &Transform, &Sprite, &PlayerInput), With<Fighter>>,
    game_state: Res<State<GameState>>,
    fight_state: Res<State<FightState>>,
    keyboard: Res<ButtonInput<KeyCode>>,
    time: Res<Time>,
    window_query: Query<&Window, With<PrimaryWindow>>,
) {
    if !qa_config.integration_testing_enabled {
        return;
    }

    let current_time = Instant::now();
    let current_frame = (time.elapsed_secs() * 60.0) as u64;

    // Initialize full game flow test if not started
    if qa_results.live_test_results.full_game_flow_test_started.is_none() {
        qa_results.live_test_results.full_game_flow_test_started = Some(current_time);
        info!("🎮 Starting Full Game Flow Testing");
    }

    // Test 1: ROM Loading → Asset Extraction Pipeline
    test_rom_to_asset_pipeline(&mut qa_results, &rom_data, &extraction_progress, &extracted_sprites, &extracted_audio);

    // Test 2: Character Spawning → Visual Rendering Pipeline
    test_character_visual_pipeline(&mut qa_results, &fighter_query, current_time);

    // Test 3: Input Handling → Game Logic Pipeline
    test_input_game_logic_pipeline(&mut qa_results, &fighter_query, &keyboard, current_frame);

    // Test 4: Game State Management Pipeline
    test_game_state_pipeline(&mut qa_results, &game_state, &fight_state, current_time);

    // Test 5: Performance and Stability Pipeline
    test_performance_stability_pipeline(&mut qa_results, current_frame, &window_query);

    // Evaluate overall full game flow test completion
    evaluate_full_game_flow_completion(&mut qa_results, current_time);
}

/// Test ROM loading to asset extraction pipeline
fn test_rom_to_asset_pipeline(
    qa_results: &mut QATestResults,
    rom_data: &Option<Res<RomData>>,
    extraction_progress: &Option<Res<AssetExtractionProgress>>,
    extracted_sprites: &Option<Res<ExtractedSprites>>,
    extracted_audio: &Option<Res<ExtractedAudio>>,
) {
    if qa_results.live_test_results.rom_to_asset_pipeline_passed {
        return; // Already passed
    }

    let mut pipeline_steps_passed = 0;
    let total_pipeline_steps = 4;

    // Step 1: ROM Loading
    if let Some(rom) = rom_data {
        if rom.loaded_roms.is_some() && rom.loading_error.is_none() {
            pipeline_steps_passed += 1;
            debug!("✅ ROM Loading step passed");
        }
    }

    // Step 2: Asset Extraction Progress
    if let Some(progress) = extraction_progress {
        if progress.is_complete {
            pipeline_steps_passed += 1;
            debug!("✅ Asset Extraction step passed");
        }
    }

    // Step 3: Sprite Extraction
    if let Some(sprites) = extracted_sprites {
        if !sprites.sprites.is_empty() {
            pipeline_steps_passed += 1;
            debug!("✅ Sprite Extraction step passed - {} sprites", sprites.sprites.len());
        }
    }

    // Step 4: Audio Extraction
    if let Some(audio) = extracted_audio {
        if !audio.samples.is_empty() {
            pipeline_steps_passed += 1;
            debug!("✅ Audio Extraction step passed - {} samples", audio.samples.len());
        }
    }

    if pipeline_steps_passed == total_pipeline_steps {
        qa_results.live_test_results.rom_to_asset_pipeline_passed = true;
        info!("🎯 ROM → Asset Extraction Pipeline: PASSED");
    } else {
        debug!("🔄 ROM → Asset Extraction Pipeline: {}/{} steps completed", pipeline_steps_passed, total_pipeline_steps);
    }
}

/// Test character spawning to visual rendering pipeline
fn test_character_visual_pipeline(
    qa_results: &mut QATestResults,
    fighter_query: &Query<(Entity, &Fighter, &Position, &Health, &Transform, &Sprite, &PlayerInput), With<Fighter>>,
    current_time: Instant,
) {
    if qa_results.live_test_results.character_visual_pipeline_passed {
        return; // Already passed
    }

    let mut pipeline_steps_passed = 0;
    let total_pipeline_steps = 4;
    let mut fighter_details = Vec::new();

    for (entity, fighter, position, health, transform, sprite, _input) in fighter_query.iter() {
        // Step 1: Fighter Entity Spawned
        pipeline_steps_passed += 1;

        // Step 2: Game Logic Components Present (Position, Health)
        if health.current > 0 {
            pipeline_steps_passed += 1;
        }

        // Step 3: Visual Components Present (Transform, Sprite)
        if transform.translation.x != 0.0 || transform.translation.y != 0.0 {
            pipeline_steps_passed += 1;
        }

        // Step 4: Position Synchronization (Game Logic ↔ Visual)
        let pos_diff_x = (position.x.to_f32() - transform.translation.x).abs();
        let pos_diff_y = (position.y.to_f32() - transform.translation.y).abs();

        if pos_diff_x < 1.0 && pos_diff_y < 1.0 {
            pipeline_steps_passed += 1;
        }

        fighter_details.push(format!(
            "Fighter {:?}: Pos({:.1}, {:.1}) → Visual({:.1}, {:.1}) Health:{} Color:{:?}",
            fighter.fighter_id,
            position.x.to_f32(), position.y.to_f32(),
            transform.translation.x, transform.translation.y,
            health.current,
            sprite.color
        ));

        break; // Test with first fighter for now
    }

    if pipeline_steps_passed >= total_pipeline_steps && fighter_query.iter().count() >= 2 {
        qa_results.live_test_results.character_visual_pipeline_passed = true;
        qa_results.live_test_results.character_visual_pipeline_time = Some(current_time);
        info!("🎯 Character → Visual Rendering Pipeline: PASSED");
        for detail in fighter_details {
            debug!("  {}", detail);
        }
    } else {
        debug!("🔄 Character → Visual Pipeline: {}/{} steps, {} fighters",
               pipeline_steps_passed, total_pipeline_steps, fighter_query.iter().count());
    }
}

/// Test input handling to game logic pipeline
fn test_input_game_logic_pipeline(
    qa_results: &mut QATestResults,
    fighter_query: &Query<(Entity, &Fighter, &Position, &Health, &Transform, &Sprite, &PlayerInput), With<Fighter>>,
    keyboard: &Res<ButtonInput<KeyCode>>,
    current_frame: u64,
) {
    if qa_results.live_test_results.input_game_logic_pipeline_passed {
        return; // Already passed
    }

    let mut pipeline_steps_passed = 0;
    let total_pipeline_steps = 3;

    // Step 1: Keyboard Input Detection
    if keyboard.get_pressed().count() > 0 {
        pipeline_steps_passed += 1;
        debug!("✅ Keyboard input detected: {:?}", keyboard.get_pressed().collect::<Vec<_>>());
    }

    // Step 2: Input System Processing
    for (_entity, _fighter, _position, _health, _transform, _sprite, player_input) in fighter_query.iter() {
        if player_input.current_input.frame > 0 && current_frame > 60 {
            pipeline_steps_passed += 1;
            debug!("✅ Input system processing: Frame {}", player_input.current_input.frame);
        }
        break; // Test with first fighter
    }

    // Step 3: Game Logic Response (frame advancement)
    if current_frame > 120 { // Allow some time for game loop to stabilize
        pipeline_steps_passed += 1;
        debug!("✅ Game logic advancement: Frame {}", current_frame);
    }

    if pipeline_steps_passed >= total_pipeline_steps {
        qa_results.live_test_results.input_game_logic_pipeline_passed = true;
        info!("🎯 Input → Game Logic Pipeline: PASSED");
    } else {
        debug!("🔄 Input → Game Logic Pipeline: {}/{} steps completed", pipeline_steps_passed, total_pipeline_steps);
    }
}

/// Test game state management pipeline
fn test_game_state_pipeline(
    qa_results: &mut QATestResults,
    game_state: &Res<State<GameState>>,
    fight_state: &Res<State<FightState>>,
    current_time: Instant,
) {
    if qa_results.live_test_results.game_state_pipeline_passed {
        return; // Already passed
    }

    let mut pipeline_steps_passed = 0;
    let total_pipeline_steps = 2;

    // Step 1: Valid Game State
    let current_game_state = *game_state.get();
    if matches!(current_game_state,
        GameState::Loading | GameState::MainMenu | GameState::CharacterSelect |
        GameState::InGame | GameState::Paused | GameState::GameOver | GameState::Credits) {
        pipeline_steps_passed += 1;
        debug!("✅ Valid game state: {:?}", current_game_state);
    }

    // Step 2: Valid Fight State
    let current_fight_state = *fight_state.get();
    if matches!(current_fight_state,
        FightState::PreFight | FightState::Fighting | FightState::RoundEnd | FightState::Victory) {
        pipeline_steps_passed += 1;
        debug!("✅ Valid fight state: {:?}", current_fight_state);
    }

    if pipeline_steps_passed >= total_pipeline_steps {
        qa_results.live_test_results.game_state_pipeline_passed = true;
        qa_results.live_test_results.game_state_pipeline_time = Some(current_time);
        info!("🎯 Game State Management Pipeline: PASSED");
    } else {
        debug!("🔄 Game State Pipeline: {}/{} steps completed", pipeline_steps_passed, total_pipeline_steps);
    }
}

/// Test performance and stability pipeline
fn test_performance_stability_pipeline(
    qa_results: &mut QATestResults,
    current_frame: u64,
    window_query: &Query<&Window, With<PrimaryWindow>>,
) {
    if qa_results.live_test_results.performance_stability_pipeline_passed {
        return; // Already passed
    }

    let mut pipeline_steps_passed = 0;
    let total_pipeline_steps = 3;

    // Step 1: Frame Rate Stability (60+ frames processed)
    if current_frame > 60 {
        pipeline_steps_passed += 1;
        debug!("✅ Frame rate stability: {} frames processed", current_frame);
    }

    // Step 2: Window Stability
    if let Ok(window) = window_query.get_single() {
        if window.resolution.width() > 0.0 && window.resolution.height() > 0.0 {
            pipeline_steps_passed += 1;
            debug!("✅ Window stability: {}x{}", window.resolution.width(), window.resolution.height());
        }
    }

    // Step 3: Extended Runtime (3+ seconds)
    if current_frame > 180 { // 3 seconds at 60 FPS
        pipeline_steps_passed += 1;
        debug!("✅ Extended runtime stability: {:.1}s", current_frame as f32 / 60.0);
    }

    if pipeline_steps_passed >= total_pipeline_steps {
        qa_results.live_test_results.performance_stability_pipeline_passed = true;
        info!("🎯 Performance & Stability Pipeline: PASSED");
    } else {
        debug!("🔄 Performance & Stability Pipeline: {}/{} steps completed", pipeline_steps_passed, total_pipeline_steps);
    }
}

/// Evaluate overall full game flow test completion
fn evaluate_full_game_flow_completion(
    qa_results: &mut QATestResults,
    current_time: Instant,
) {
    if qa_results.live_test_results.full_game_flow_test_completed {
        return; // Already completed
    }

    let all_pipelines_passed =
        qa_results.live_test_results.rom_to_asset_pipeline_passed &&
        qa_results.live_test_results.character_visual_pipeline_passed &&
        qa_results.live_test_results.input_game_logic_pipeline_passed &&
        qa_results.live_test_results.game_state_pipeline_passed &&
        qa_results.live_test_results.performance_stability_pipeline_passed;

    if all_pipelines_passed {
        qa_results.live_test_results.full_game_flow_test_completed = true;
        qa_results.live_test_results.full_game_flow_completion_time = Some(current_time);

        let test_duration = if let Some(start_time) = qa_results.live_test_results.full_game_flow_test_started {
            current_time.duration_since(start_time)
        } else {
            Duration::from_secs(0)
        };

        info!("🏆 FULL GAME FLOW TEST COMPLETED SUCCESSFULLY!");
        info!("📊 Test Duration: {:.2}s", test_duration.as_secs_f32());
        info!("✅ All 5 pipelines passed:");
        info!("  1. ROM → Asset Extraction Pipeline");
        info!("  2. Character → Visual Rendering Pipeline");
        info!("  3. Input → Game Logic Pipeline");
        info!("  4. Game State Management Pipeline");
        info!("  5. Performance & Stability Pipeline");

        // Print detailed summary
        print_full_game_flow_summary(&qa_results.live_test_results);
    } else {
        // Log progress every 5 seconds
        if let Some(start_time) = qa_results.live_test_results.full_game_flow_test_started {
            let elapsed = current_time.duration_since(start_time);
            if elapsed.as_secs() % 5 == 0 && elapsed.as_secs() > 0 {
                debug!("🔄 Full Game Flow Test Progress: {:.1}s elapsed", elapsed.as_secs_f32());
                debug!("  ROM→Asset: {}", if qa_results.live_test_results.rom_to_asset_pipeline_passed { "✅" } else { "⏳" });
                debug!("  Character→Visual: {}", if qa_results.live_test_results.character_visual_pipeline_passed { "✅" } else { "⏳" });
                debug!("  Input→Logic: {}", if qa_results.live_test_results.input_game_logic_pipeline_passed { "✅" } else { "⏳" });
                debug!("  Game State: {}", if qa_results.live_test_results.game_state_pipeline_passed { "✅" } else { "⏳" });
                debug!("  Performance: {}", if qa_results.live_test_results.performance_stability_pipeline_passed { "✅" } else { "⏳" });
            }
        }
    }
}

fn sample_performance_metrics(metrics: &mut PerformanceTestResults, fps: f32, config: &QATestingConfig) {
    // Add FPS sample
    metrics.fps_samples.push_back(fps);
    if metrics.fps_samples.len() > 1000 {
        metrics.fps_samples.pop_front();
    }
    
    // Update min/max FPS
    metrics.min_fps = metrics.min_fps.min(fps);
    metrics.max_fps = metrics.max_fps.max(fps);
    
    // Calculate average FPS
    metrics.average_fps = metrics.fps_samples.iter().sum::<f32>() / metrics.fps_samples.len() as f32;
    
    // Count frame drops
    if fps < config.target_fps * 0.9 {
        metrics.frame_drops += 1;
    }
}

fn check_performance_issues(metrics: &mut PerformanceTestResults, config: &QATestingConfig) {
    // Check for sustained low FPS
    if metrics.average_fps < config.target_fps * 0.8 {
        let issue = format!("Average FPS ({:.1}) below 80% of target ({:.1})", 
                           metrics.average_fps, config.target_fps);
        if !metrics.performance_issues.contains(&issue) {
            metrics.performance_issues.push(issue);
        }
    }
    
    // Check for excessive frame drops
    if metrics.frame_drops > 100 {
        let issue = format!("Excessive frame drops detected: {}", metrics.frame_drops);
        if !metrics.performance_issues.contains(&issue) {
            metrics.performance_issues.push(issue);
        }
    }
}

fn log_performance_summary(metrics: &PerformanceTestResults) {
    info!("=== Performance Summary ===");
    info!("Average FPS: {:.1}", metrics.average_fps);
    info!("Min FPS: {:.1}", metrics.min_fps);
    info!("Max FPS: {:.1}", metrics.max_fps);
    info!("Frame drops: {}", metrics.frame_drops);
    if !metrics.performance_issues.is_empty() {
        warn!("Performance issues: {:?}", metrics.performance_issues);
    }
}

impl IntegrationTestResults {
    fn all_tests_passed(&self) -> bool {
        self.rom_loading_passed &&
        self.asset_extraction_passed &&
        self.game_init_passed &&
        self.state_transitions_passed &&
        self.character_system_passed &&
        self.input_system_passed &&
        self.collision_system_passed
    }
}

/// Print comprehensive full game flow test summary
fn print_full_game_flow_summary(results: &LiveTestResults) {
    info!("📋 FULL GAME FLOW TEST SUMMARY");
    info!("================================");

    if let Some(start_time) = results.full_game_flow_test_started {
        if let Some(completion_time) = results.full_game_flow_completion_time {
            let duration = completion_time.duration_since(start_time);
            info!("⏱️  Total Test Duration: {:.2}s", duration.as_secs_f32());
        }
    }

    info!("🔄 Pipeline Results:");
    info!("  1. ROM → Asset Extraction: {}", if results.rom_to_asset_pipeline_passed { "✅ PASSED" } else { "❌ FAILED" });
    info!("  2. Character → Visual Rendering: {}", if results.character_visual_pipeline_passed { "✅ PASSED" } else { "❌ FAILED" });
    info!("  3. Input → Game Logic: {}", if results.input_game_logic_pipeline_passed { "✅ PASSED" } else { "❌ FAILED" });
    info!("  4. Game State Management: {}", if results.game_state_pipeline_passed { "✅ PASSED" } else { "❌ FAILED" });
    info!("  5. Performance & Stability: {}", if results.performance_stability_pipeline_passed { "✅ PASSED" } else { "❌ FAILED" });

    info!("🎯 Overall Result: {}", if results.full_game_flow_test_completed { "✅ SUCCESS" } else { "⏳ IN PROGRESS" });
    info!("📊 Runtime: {:.1}s", results.runtime_seconds);
    info!("🔄 State Changes: {}", results.state_changes.len());
    info!("⚡ Stability Rating: {:.1}%", results.stability_rating * 100.0);
    info!("================================");
}
