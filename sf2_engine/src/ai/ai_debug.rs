// AI Debug System - comprehensive AI debugging and tuning tools
// Provides real-time AI state visualization, performance metrics, and tuning capabilities

use bevy::prelude::*;
use bevy::input::ButtonInput as BevyButtonInput;
use bevy::gizmos::gizmos::Gizmos;
use serde::{Deserialize, Serialize};
use crate::ai::ai_core::*;
use crate::ai::ai_difficulty::{AIDifficulty, AIPerformanceMetrics};
use crate::ai::ai_threat::*;
use crate::Fighter;
use sf2_types::character_traits::AIBehaviorPattern;
use std::collections::{HashMap, VecDeque};

/// AI Debug Component for Bevy ECS
#[derive(Component, Debug, Clone, Serialize, Deserialize)]
pub struct AIDebugInfo {
    /// Debug mode enabled
    pub enabled: bool,
    /// Current debug level (0 = off, 1 = basic, 2 = detailed, 3 = verbose)
    pub debug_level: u8,
    /// Performance metrics
    pub performance_metrics: AIPerformanceMetrics,
    /// Decision history for analysis
    pub decision_history: VecDeque<AIDecision>,
    /// Strategy execution log
    pub strategy_log: VecDeque<StrategyLogEntry>,
    /// Threat assessment log
    pub threat_log: VecDeque<ThreatLogEntry>,
    /// Frame-by-frame state snapshots
    pub state_snapshots: VecDeque<AIStateSnapshot>,
    /// Maximum history length
    pub max_history_length: usize,
    /// Debug visualization settings
    pub visualization: AIDebugVisualization,
}



/// AI Decision record for analysis
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AIDecision {
    /// Frame when decision was made
    pub frame: u64,
    /// Decision type
    pub decision_type: AIDecisionType,
    /// Input factors that influenced the decision
    pub input_factors: DecisionInputs,
    /// Confidence level (0.0 to 1.0)
    pub confidence: f32,
    /// Outcome of the decision (filled in later)
    pub outcome: Option<DecisionOutcome>,
    /// Time taken to make decision (microseconds)
    pub decision_time_us: u64,
}

/// Types of AI decisions
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum AIDecisionType {
    Attack,
    Defend,
    Move,
    Jump,
    Special,
    Throw,
    Block,
    Wait,
    StrategyChange,
}

/// Input factors for decision making
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DecisionInputs {
    /// Distance to opponent
    pub opponent_distance: i16,
    /// Opponent state
    pub opponent_state: String,
    /// Current threat level
    pub threat_level: f32,
    /// AI health percentage
    pub ai_health_percent: f32,
    /// Opponent health percentage
    pub opponent_health_percent: f32,
    /// Time remaining in round
    pub time_remaining: u16,
    /// Current AI strategy
    pub current_strategy: String,
}

/// Decision outcome
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum DecisionOutcome {
    Success,
    Failure,
    Neutral,
    Interrupted,
}

/// Strategy execution log entry
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StrategyLogEntry {
    /// Frame when strategy was executed
    pub frame: u64,
    /// Strategy name
    pub strategy_name: String,
    /// Bytecode instruction executed
    pub instruction: String,
    /// Parameters used
    pub parameters: Vec<u8>,
    /// Execution result
    pub result: StrategyExecutionResult,
}

/// Strategy execution result
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum StrategyExecutionResult {
    Success,
    Failed,
    Interrupted,
    Completed,
}

/// Threat assessment log entry
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThreatLogEntry {
    /// Frame when threat was assessed
    pub frame: u64,
    /// Threat level calculated
    pub threat_level: f32,
    /// Threat type detected
    pub threat_type: String,
    /// Distance to threat
    pub threat_distance: i16,
    /// Response chosen
    pub response: String,
}

/// AI state snapshot for frame-by-frame analysis
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AIStateSnapshot {
    /// Frame number
    pub frame: u64,
    /// AI mode states
    pub mode1: String,
    pub mode2: String,
    /// Current strategy
    pub current_strategy: String,
    /// AI flags
    pub flags: HashMap<String, bool>,
    /// Timer values
    pub timers: HashMap<String, u8>,
    /// Position and movement
    pub position: Vec2,
    pub velocity: Vec2,
}

/// Reaction time statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ReactionTimeStats {
    /// Average reaction time (frames)
    pub avg_reaction_time: f32,
    /// Minimum reaction time
    pub min_reaction_time: u16,
    /// Maximum reaction time
    pub max_reaction_time: u16,
    /// Reaction time samples
    pub samples: VecDeque<u16>,
}

/// Win/loss statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WinLossStats {
    /// Total rounds played
    pub total_rounds: u32,
    /// Rounds won
    pub rounds_won: u32,
    /// Rounds lost
    pub rounds_lost: u32,
    /// Win rate
    pub win_rate: f32,
    /// Average round duration
    pub avg_round_duration: f32,
}

/// Debug visualization settings
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AIDebugVisualization {
    /// Show AI state overlay
    pub show_state_overlay: bool,
    /// Show threat zones
    pub show_threat_zones: bool,
    /// Show decision tree
    pub show_decision_tree: bool,
    /// Show strategy execution
    pub show_strategy_execution: bool,
    /// Show performance metrics
    pub show_performance_metrics: bool,
    /// Overlay position
    pub overlay_position: Vec2,
    /// Text color
    pub text_color: Color,
    /// Background color
    pub background_color: Color,
}

impl Default for AIDebugInfo {
    fn default() -> Self {
        Self {
            enabled: false,
            debug_level: 0,
            performance_metrics: AIPerformanceMetrics::default(),
            decision_history: VecDeque::new(),
            strategy_log: VecDeque::new(),
            threat_log: VecDeque::new(),
            state_snapshots: VecDeque::new(),
            max_history_length: 300, // 5 seconds at 60fps
            visualization: AIDebugVisualization::default(),
        }
    }
}



impl Default for ReactionTimeStats {
    fn default() -> Self {
        Self {
            avg_reaction_time: 15.0,
            min_reaction_time: u16::MAX,
            max_reaction_time: 0,
            samples: VecDeque::new(),
        }
    }
}

impl Default for WinLossStats {
    fn default() -> Self {
        Self {
            total_rounds: 0,
            rounds_won: 0,
            rounds_lost: 0,
            win_rate: 0.0,
            avg_round_duration: 0.0,
        }
    }
}

impl Default for AIDebugVisualization {
    fn default() -> Self {
        Self {
            show_state_overlay: false,
            show_threat_zones: false,
            show_decision_tree: false,
            show_strategy_execution: false,
            show_performance_metrics: false,
            overlay_position: Vec2::new(10.0, 10.0),
            text_color: Color::WHITE,
            background_color: Color::srgba(0.0, 0.0, 0.0, 0.7),
        }
    }
}

impl AIDebugInfo {
    /// Initialize debug system from environment variables
    pub fn init_from_env() -> Self {
        let mut debug_info = Self::default();
        
        // Check for debug environment variables
        if let Ok(debug_level) = std::env::var("AI_DEBUG_LEVEL") {
            if let Ok(level) = debug_level.parse::<u8>() {
                debug_info.debug_level = level.min(3);
                debug_info.enabled = level > 0;
            }
        }
        
        if let Ok(_) = std::env::var("AI_DEBUG_ENABLED") {
            debug_info.enabled = true;
            if debug_info.debug_level == 0 {
                debug_info.debug_level = 1;
            }
        }
        
        // Configure visualization from environment
        if let Ok(_) = std::env::var("AI_DEBUG_SHOW_STATE") {
            debug_info.visualization.show_state_overlay = true;
        }
        
        if let Ok(_) = std::env::var("AI_DEBUG_SHOW_THREATS") {
            debug_info.visualization.show_threat_zones = true;
        }
        
        if let Ok(_) = std::env::var("AI_DEBUG_SHOW_STRATEGY") {
            debug_info.visualization.show_strategy_execution = true;
        }
        
        debug_info
    }
    
    /// Record an AI decision
    pub fn record_decision(&mut self, decision: AIDecision, frame: u64) {
        if !self.enabled || self.debug_level < 1 {
            return;
        }
        
        self.decision_history.push_back(decision);
        self.performance_metrics.total_frames += 1;
        
        // Maintain history size
        if self.decision_history.len() > self.max_history_length {
            self.decision_history.pop_front();
        }
    }
    
    /// Record strategy execution
    pub fn record_strategy_execution(&mut self, entry: StrategyLogEntry) {
        if !self.enabled || self.debug_level < 2 {
            return;
        }
        
        self.strategy_log.push_back(entry);
        
        if self.strategy_log.len() > self.max_history_length {
            self.strategy_log.pop_front();
        }
    }
    
    /// Record threat assessment
    pub fn record_threat_assessment(&mut self, entry: ThreatLogEntry) {
        if !self.enabled || self.debug_level < 2 {
            return;
        }
        
        self.threat_log.push_back(entry);
        
        if self.threat_log.len() > self.max_history_length {
            self.threat_log.pop_front();
        }
    }
    
    /// Take state snapshot
    pub fn take_state_snapshot(&mut self, snapshot: AIStateSnapshot) {
        if !self.enabled || self.debug_level < 3 {
            return;
        }
        
        self.state_snapshots.push_back(snapshot);
        
        if self.state_snapshots.len() > self.max_history_length {
            self.state_snapshots.pop_front();
        }
    }
    
    /// Update performance metrics
    pub fn update_performance_metrics(&mut self, decision_outcome: DecisionOutcome, decision_time_us: u64) {
        if !self.enabled {
            return;
        }
        
        // Update decision success rate
        if decision_outcome == DecisionOutcome::Success {
            self.performance_metrics.hit_ratio = (self.performance_metrics.hit_ratio * 0.99 + 0.01).min(1.0);
        }

        // Update average reaction time (convert microseconds to frames at 60fps)
        let reaction_time_frames = decision_time_us as f32 / 16666.67; // 1 frame = ~16.67ms
        self.performance_metrics.average_reaction_time =
            self.performance_metrics.average_reaction_time * 0.9 + reaction_time_frames * 0.1;
    }
    
    /// Update reaction time statistics
    pub fn update_reaction_time(&mut self, reaction_time_frames: u16) {
        if !self.enabled {
            return;
        }

        // Update average reaction time
        self.performance_metrics.average_reaction_time =
            self.performance_metrics.average_reaction_time * 0.9 + reaction_time_frames as f32 * 0.1;

    }
    
    /// Update win/loss statistics
    pub fn update_win_loss_stats(&mut self, won: bool, round_duration_frames: u32) {
        if !self.enabled {
            return;
        }

        // Update win/loss ratio
        if won {
            self.performance_metrics.win_loss_ratio = (self.performance_metrics.win_loss_ratio * 0.9 + 0.1).min(1.0);
        } else {
            self.performance_metrics.win_loss_ratio = (self.performance_metrics.win_loss_ratio * 0.9).max(0.0);
        }

    }
    
    /// Generate debug report
    pub fn generate_debug_report(&self) -> String {
        if !self.enabled {
            return "AI Debug disabled".to_string();
        }
        
        let mut report = String::new();
        
        report.push_str("=== AI Debug Report ===\n");
        report.push_str(&format!("Debug Level: {}\n", self.debug_level));
        report.push_str(&format!("Total Frames: {}\n", self.performance_metrics.total_frames));
        report.push_str(&format!("Hit Ratio: {:.2}%\n", self.performance_metrics.hit_ratio * 100.0));
        report.push_str(&format!("Combo Success Rate: {:.2}%\n", self.performance_metrics.combo_success_rate * 100.0));
        report.push_str(&format!("Avg Reaction Time: {:.1} frames\n", self.performance_metrics.average_reaction_time));
        report.push_str(&format!("Win/Loss Ratio: {:.2}\n", self.performance_metrics.win_loss_ratio));
        report.push_str(&format!("Special Move Usage: {:.2}%\n", self.performance_metrics.special_move_usage * 100.0));
        report.push_str(&format!("Defensive Success Rate: {:.2}%\n", self.performance_metrics.defensive_success_rate * 100.0));
        
        if self.debug_level >= 2 {
            report.push_str("\n=== Recent Decisions ===\n");
            for decision in self.decision_history.iter().rev().take(5) {
                report.push_str(&format!("Frame {}: {:?} (confidence: {:.2})\n", 
                    decision.frame, decision.decision_type, decision.confidence));
            }
        }
        
        if self.debug_level >= 3 {
            report.push_str("\n=== Recent Strategy Executions ===\n");
            for entry in self.strategy_log.iter().rev().take(5) {
                report.push_str(&format!("Frame {}: {} - {} ({:?})\n", 
                    entry.frame, entry.strategy_name, entry.instruction, entry.result));
            }
        }
        
        report
    }
    
    /// Export debug data to JSON
    pub fn export_to_json(&self) -> Result<String, serde_json::Error> {
        serde_json::to_string_pretty(self)
    }
    
    /// Clear all debug history
    pub fn clear_history(&mut self) {
        self.decision_history.clear();
        self.strategy_log.clear();
        self.threat_log.clear();
        self.state_snapshots.clear();
        self.performance_metrics = AIPerformanceMetrics::default();
    }
}

// ============================================================================
// AI Debug Configuration and Environment Variables
// ============================================================================

/// AI Debug Configuration Resource
#[derive(Resource, Debug, Clone, Serialize, Deserialize)]
pub struct AIDebugConfig {
    /// Global debug enabled
    pub debug_enabled: bool,
    /// Debug level (0-3)
    pub debug_level: u8,
    /// Performance monitoring enabled
    pub performance_monitoring: bool,
    /// Visual debugging enabled
    pub visual_debugging: bool,
    /// Log to file enabled
    pub log_to_file: bool,
    /// Log file path
    pub log_file_path: String,
    /// Real-time tuning enabled
    pub real_time_tuning: bool,
    /// Auto-export debug data
    pub auto_export: bool,
    /// Export interval (frames)
    pub export_interval: u64,
}

impl Default for AIDebugConfig {
    fn default() -> Self {
        Self {
            debug_enabled: false,
            debug_level: 0,
            performance_monitoring: true,
            visual_debugging: false,
            log_to_file: false,
            log_file_path: "ai_debug.log".to_string(),
            real_time_tuning: false,
            auto_export: false,
            export_interval: 3600, // 1 minute at 60fps
        }
    }
}

impl AIDebugConfig {
    /// Initialize from environment variables
    pub fn from_env() -> Self {
        let mut config = Self::default();

        // AI_DEBUG_ENABLED - Enable AI debugging
        if let Ok(value) = std::env::var("AI_DEBUG_ENABLED") {
            config.debug_enabled = value.parse().unwrap_or(true);
        }

        // AI_DEBUG_LEVEL - Debug verbosity level (0-3)
        if let Ok(value) = std::env::var("AI_DEBUG_LEVEL") {
            if let Ok(level) = value.parse::<u8>() {
                config.debug_level = level.min(3);
                config.debug_enabled = level > 0;
            }
        }

        // AI_DEBUG_PERFORMANCE - Enable performance monitoring
        if let Ok(value) = std::env::var("AI_DEBUG_PERFORMANCE") {
            config.performance_monitoring = value.parse().unwrap_or(true);
        }

        // AI_DEBUG_VISUAL - Enable visual debugging overlays
        if let Ok(value) = std::env::var("AI_DEBUG_VISUAL") {
            config.visual_debugging = value.parse().unwrap_or(false);
        }

        // AI_DEBUG_LOG_FILE - Enable logging to file
        if let Ok(value) = std::env::var("AI_DEBUG_LOG_FILE") {
            config.log_to_file = value.parse().unwrap_or(false);
        }

        // AI_DEBUG_LOG_PATH - Log file path
        if let Ok(value) = std::env::var("AI_DEBUG_LOG_PATH") {
            config.log_file_path = value;
        }

        // AI_DEBUG_TUNING - Enable real-time tuning
        if let Ok(value) = std::env::var("AI_DEBUG_TUNING") {
            config.real_time_tuning = value.parse().unwrap_or(false);
        }

        // AI_DEBUG_AUTO_EXPORT - Auto-export debug data
        if let Ok(value) = std::env::var("AI_DEBUG_AUTO_EXPORT") {
            config.auto_export = value.parse().unwrap_or(false);
        }

        // AI_DEBUG_EXPORT_INTERVAL - Export interval in frames
        if let Ok(value) = std::env::var("AI_DEBUG_EXPORT_INTERVAL") {
            if let Ok(interval) = value.parse::<u64>() {
                config.export_interval = interval;
            }
        }

        config
    }
}

// ============================================================================
// AI Tuning Interface
// ============================================================================

/// AI Tuning Interface for real-time parameter adjustment
#[derive(Resource, Debug, Clone, Serialize, Deserialize)]
pub struct AITuningInterface {
    /// Tuning enabled
    pub enabled: bool,
    /// Current tuning target
    pub target_entity: Option<Entity>,
    /// Tuning parameters
    pub tuning_params: AITuningParams,
    /// Parameter change history
    pub change_history: VecDeque<ParameterChange>,
    /// Auto-save enabled
    pub auto_save: bool,
}

/// AI Tuning Parameters
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AITuningParams {
    /// Difficulty adjustment
    pub difficulty_modifier: f32,
    /// Reaction time multiplier
    pub reaction_time_multiplier: f32,
    /// Aggression level override
    pub aggression_override: Option<f32>,
    /// Defense level override
    pub defense_override: Option<f32>,
    /// Special move frequency override
    pub special_frequency_override: Option<f32>,
    /// Jump frequency override
    pub jump_frequency_override: Option<f32>,
    /// Threat sensitivity multiplier
    pub threat_sensitivity_multiplier: f32,
    /// Decision confidence threshold
    pub decision_confidence_threshold: f32,
}

/// Parameter change record
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ParameterChange {
    /// Frame when change was made
    pub frame: u64,
    /// Parameter name
    pub parameter_name: String,
    /// Old value
    pub old_value: f32,
    /// New value
    pub new_value: f32,
    /// Reason for change
    pub reason: String,
}

impl Default for AITuningInterface {
    fn default() -> Self {
        Self {
            enabled: false,
            target_entity: None,
            tuning_params: AITuningParams::default(),
            change_history: VecDeque::new(),
            auto_save: false,
        }
    }
}

impl Default for AITuningParams {
    fn default() -> Self {
        Self {
            difficulty_modifier: 1.0,
            reaction_time_multiplier: 1.0,
            aggression_override: None,
            defense_override: None,
            special_frequency_override: None,
            jump_frequency_override: None,
            threat_sensitivity_multiplier: 1.0,
            decision_confidence_threshold: 0.5,
        }
    }
}

impl AITuningInterface {
    /// Initialize from environment variables
    pub fn from_env() -> Self {
        let mut interface = Self::default();

        // AI_TUNING_ENABLED - Enable real-time tuning
        if let Ok(value) = std::env::var("AI_TUNING_ENABLED") {
            interface.enabled = value.parse().unwrap_or(false);
        }

        // AI_TUNING_AUTO_SAVE - Auto-save tuning changes
        if let Ok(value) = std::env::var("AI_TUNING_AUTO_SAVE") {
            interface.auto_save = value.parse().unwrap_or(false);
        }

        // Load tuning parameters from environment
        interface.tuning_params = AITuningParams::from_env();

        interface
    }

    /// Apply tuning parameters to AI difficulty
    pub fn apply_to_difficulty(&self, difficulty: &mut AIDifficulty) {
        if !self.enabled {
            return;
        }

        // Apply difficulty modifier
        difficulty.base_difficulty = (difficulty.base_difficulty as f32 * self.tuning_params.difficulty_modifier) as u8;
        difficulty.base_difficulty = difficulty.base_difficulty.clamp(1, 32);

        // Apply reaction time multiplier
        difficulty.reaction_time_frames = (difficulty.reaction_time_frames as f32 * self.tuning_params.reaction_time_multiplier) as u16;
        difficulty.reaction_time_frames = difficulty.reaction_time_frames.clamp(1, 120);

        // Note: decision_confidence_threshold is not part of AIDifficulty structure
        // This would need to be applied to a different component if needed
    }

    /// Record parameter change
    pub fn record_change(&mut self, parameter_name: String, old_value: f32, new_value: f32, reason: String, frame: u64) {
        let change = ParameterChange {
            frame,
            parameter_name,
            old_value,
            new_value,
            reason,
        };

        self.change_history.push_back(change);

        // Maintain history size
        if self.change_history.len() > 100 {
            self.change_history.pop_front();
        }
    }
}

impl AITuningParams {
    /// Initialize from environment variables
    pub fn from_env() -> Self {
        let mut params = Self::default();

        // AI_TUNING_DIFFICULTY - Difficulty modifier
        if let Ok(value) = std::env::var("AI_TUNING_DIFFICULTY") {
            if let Ok(modifier) = value.parse::<f32>() {
                params.difficulty_modifier = modifier.clamp(0.1, 5.0);
            }
        }

        // AI_TUNING_REACTION_TIME - Reaction time multiplier
        if let Ok(value) = std::env::var("AI_TUNING_REACTION_TIME") {
            if let Ok(multiplier) = value.parse::<f32>() {
                params.reaction_time_multiplier = multiplier.clamp(0.1, 10.0);
            }
        }

        // AI_TUNING_AGGRESSION - Aggression override
        if let Ok(value) = std::env::var("AI_TUNING_AGGRESSION") {
            if let Ok(aggression) = value.parse::<f32>() {
                params.aggression_override = Some(aggression.clamp(0.0, 1.0));
            }
        }

        // AI_TUNING_DEFENSE - Defense override
        if let Ok(value) = std::env::var("AI_TUNING_DEFENSE") {
            if let Ok(defense) = value.parse::<f32>() {
                params.defense_override = Some(defense.clamp(0.0, 1.0));
            }
        }

        // AI_TUNING_SPECIAL_FREQUENCY - Special move frequency override
        if let Ok(value) = std::env::var("AI_TUNING_SPECIAL_FREQUENCY") {
            if let Ok(frequency) = value.parse::<f32>() {
                params.special_frequency_override = Some(frequency.clamp(0.0, 1.0));
            }
        }

        // AI_TUNING_JUMP_FREQUENCY - Jump frequency override
        if let Ok(value) = std::env::var("AI_TUNING_JUMP_FREQUENCY") {
            if let Ok(frequency) = value.parse::<f32>() {
                params.jump_frequency_override = Some(frequency.clamp(0.0, 1.0));
            }
        }

        // AI_TUNING_THREAT_SENSITIVITY - Threat sensitivity multiplier
        if let Ok(value) = std::env::var("AI_TUNING_THREAT_SENSITIVITY") {
            if let Ok(sensitivity) = value.parse::<f32>() {
                params.threat_sensitivity_multiplier = sensitivity.clamp(0.1, 5.0);
            }
        }

        // AI_TUNING_CONFIDENCE_THRESHOLD - Decision confidence threshold
        if let Ok(value) = std::env::var("AI_TUNING_CONFIDENCE_THRESHOLD") {
            if let Ok(threshold) = value.parse::<f32>() {
                params.decision_confidence_threshold = threshold.clamp(0.0, 1.0);
            }
        }

        params
    }
}

// ============================================================================
// Bevy ECS Systems for AI Debugging and Tuning
// ============================================================================

/// AI Debug Data Collection System
pub fn ai_debug_data_collection_system(
    mut debug_query: Query<(
        Entity,
        &mut AIDebugInfo,
        &AICore,
        &AIDifficulty,
        Option<&AIThreatAssessment>,
        Option<&AIDecisionMaker>,
    ), With<Fighter>>,
    config: Res<AIDebugConfig>,
    time: Res<Time>,
) {
    if !config.debug_enabled {
        return;
    }

    let current_frame = (time.elapsed_secs() * 60.0) as u64;

    for (entity, mut debug_info, ai_core, difficulty, threat_assessment, decision_maker) in debug_query.iter_mut() {
        // Take state snapshot if debug level is high enough
        if config.debug_level >= 3 {
            let snapshot = AIStateSnapshot {
                frame: current_frame,
                mode1: format!("{:?}", ai_core.mode1),
                mode2: format!("{:?}", ai_core.mode2),
                current_strategy: format!("{:?}", ai_core.strategy_state),
                flags: {
                    let mut flags = HashMap::new();
                    flags.insert("sig_attack".to_string(), ai_core.flags.sig_attack);
                    flags.insert("sig_special".to_string(), ai_core.flags.sig_special);
                    flags.insert("volley".to_string(), ai_core.flags.volley);
                    flags.insert("force_defensive".to_string(), ai_core.flags.force_defensive);
                    flags.insert("allow_aggressive".to_string(), ai_core.flags.allow_aggressive);
                    flags.insert("threat_found".to_string(), ai_core.flags.threat_found);
                    flags.insert("can_jump_attack".to_string(), ai_core.flags.can_jump_attack);
                    flags
                },
                timers: {
                    let mut timers = HashMap::new();
                    timers.insert("main_timer".to_string(), ai_core.timers.main_timer);
                    timers.insert("agg_timer0".to_string(), ai_core.timers.agg_timer0);
                    timers.insert("agg_timer1".to_string(), ai_core.timers.agg_timer1);
                    timers.insert("threat_check_timer".to_string(), ai_core.timers.threat_check_timer);
                    timers
                },
                position: Vec2::new(0.0, 0.0), // Would need fighter position component
                velocity: Vec2::new(0.0, 0.0), // Would need fighter velocity component
            };

            debug_info.take_state_snapshot(snapshot);
        }

        // Record threat assessment if available
        if let Some(threat) = threat_assessment {
            if config.debug_level >= 2 {
                let threat_entry = ThreatLogEntry {
                    frame: current_frame,
                    threat_level: threat.threat_level,
                    threat_type: format!("{:?}", threat.threat_zones),
                    threat_distance: threat.closest_threat_distance,
                    response: format!("{:?}", threat.opponent_analysis),
                };

                debug_info.record_threat_assessment(threat_entry);
            }
        }

        // Record decision making if available
        if let Some(decision_maker) = decision_maker {
            if let Some(current_decision) = &decision_maker.current_decision {
                if config.debug_level >= 1 {
                    let decision = AIDecision {
                        frame: current_frame,
                        decision_type: match current_decision {
                            AITacticalDecision::PressureAttack => AIDecisionType::Attack,
                            AITacticalDecision::ComboAttack => AIDecisionType::Attack,
                            AITacticalDecision::ThrowAttempt => AIDecisionType::Throw,
                            AITacticalDecision::SpecialAttack(_) => AIDecisionType::Special,
                            AITacticalDecision::JumpAttack => AIDecisionType::Jump,
                            AITacticalDecision::Block => AIDecisionType::Block,
                            AITacticalDecision::Retreat => AIDecisionType::Move,
                            AITacticalDecision::AntiAir => AIDecisionType::Defend,
                            AITacticalDecision::Escape => AIDecisionType::Move,
                            AITacticalDecision::Counter => AIDecisionType::Defend,
                            AITacticalDecision::MaintainDistance => AIDecisionType::Move,
                            AITacticalDecision::CloseDistance => AIDecisionType::Move,
                            AITacticalDecision::CreateSpace => AIDecisionType::Move,
                            AITacticalDecision::CornerOpponent => AIDecisionType::Move,
                            AITacticalDecision::EscapeCorner => AIDecisionType::Move,
                            AITacticalDecision::Wait => AIDecisionType::Wait,
                            AITacticalDecision::Bait => AIDecisionType::Wait,
                            AITacticalDecision::Feint => AIDecisionType::Attack,
                            AITacticalDecision::Observe => AIDecisionType::Wait,
                        },
                        input_factors: DecisionInputs {
                            opponent_distance: decision_maker.opponent_tracker.last_position.length() as i16,
                            opponent_state: format!("{:?}", decision_maker.opponent_tracker.action_patterns),
                            threat_level: threat_assessment.map(|t| t.threat_level).unwrap_or(0.0),
                            ai_health_percent: 1.0, // Would need fighter health component
                            opponent_health_percent: 1.0, // Would need opponent health component
                            time_remaining: 99, // Would need round timer
                            current_strategy: format!("{:?}", ai_core.strategy_state),
                        },
                        confidence: decision_maker.decision_confidence,
                        outcome: None, // Will be filled in later
                        decision_time_us: 0, // Would need timing measurement
                    };

                    debug_info.record_decision(decision, current_frame);
                }
            }
        }
    }
}

/// AI Performance Monitoring System
pub fn ai_performance_monitoring_system(
    mut debug_query: Query<&mut AIDebugInfo, With<Fighter>>,
    config: Res<AIDebugConfig>,
    time: Res<Time>,
) {
    if !config.performance_monitoring {
        return;
    }

    let current_frame = (time.elapsed_secs() * 60.0) as u64;

    for mut debug_info in debug_query.iter_mut() {
        // Update performance metrics
        debug_info.performance_metrics.total_frames = current_frame;

        // Calculate average decision confidence
        if !debug_info.decision_history.is_empty() {
            let total_confidence: f32 = debug_info.decision_history.iter()
                .map(|d| d.confidence)
                .sum();
            let avg_confidence = total_confidence / debug_info.decision_history.len() as f32;

            // Update performance metrics with confidence as a proxy for decision quality
            debug_info.performance_metrics.combo_success_rate = avg_confidence;
        }
    }
}

/// AI Real-time Tuning System
pub fn ai_real_time_tuning_system(
    mut difficulty_query: Query<&mut AIDifficulty, With<Fighter>>,
    mut behavior_query: Query<&mut AIBehaviorPattern, With<Fighter>>,
    mut tuning_interface: ResMut<AITuningInterface>,
    time: Res<Time>,
) {
    if !tuning_interface.enabled {
        return;
    }

    let current_frame = (time.elapsed_secs() * 60.0) as u64;

    // Apply tuning parameters to AI difficulty
    for mut difficulty in difficulty_query.iter_mut() {
        tuning_interface.apply_to_difficulty(&mut difficulty);
    }

    // Apply tuning parameters to behavior patterns
    for mut behavior in behavior_query.iter_mut() {
        if let Some(aggression) = tuning_interface.tuning_params.aggression_override {
            if (behavior.aggression - aggression).abs() > 0.01 {
                let old_value = behavior.aggression;
                behavior.aggression = aggression;
                tuning_interface.record_change(
                    "aggression".to_string(),
                    old_value,
                    aggression,
                    "Environment variable override".to_string(),
                    current_frame,
                );
            }
        }

        if let Some(defense) = tuning_interface.tuning_params.defense_override {
            if (behavior.defense - defense).abs() > 0.01 {
                let old_value = behavior.defense;
                behavior.defense = defense;
                tuning_interface.record_change(
                    "defense".to_string(),
                    old_value,
                    defense,
                    "Environment variable override".to_string(),
                    current_frame,
                );
            }
        }

        if let Some(special_freq) = tuning_interface.tuning_params.special_frequency_override {
            if (behavior.special_move_frequency - special_freq).abs() > 0.01 {
                let old_value = behavior.special_move_frequency;
                behavior.special_move_frequency = special_freq;
                tuning_interface.record_change(
                    "special_move_frequency".to_string(),
                    old_value,
                    special_freq,
                    "Environment variable override".to_string(),
                    current_frame,
                );
            }
        }

        if let Some(jump_freq) = tuning_interface.tuning_params.jump_frequency_override {
            if (behavior.jump_frequency - jump_freq).abs() > 0.01 {
                let old_value = behavior.jump_frequency;
                behavior.jump_frequency = jump_freq;
                tuning_interface.record_change(
                    "jump_frequency".to_string(),
                    old_value,
                    jump_freq,
                    "Environment variable override".to_string(),
                    current_frame,
                );
            }
        }
    }
}

/// AI Debug Visualization System
pub fn ai_debug_visualization_system(
    debug_query: Query<&AIDebugInfo, With<Fighter>>,
    config: Res<AIDebugConfig>,
    mut gizmos: Gizmos,
) {
    if !config.visual_debugging {
        return;
    }

    for debug_info in debug_query.iter() {
        if !debug_info.enabled || !debug_info.visualization.show_state_overlay {
            continue;
        }

        // Draw threat zones (would need position data)
        if debug_info.visualization.show_threat_zones {
            // Draw threat visualization circles
            gizmos.circle_2d(Vec2::new(100.0, 100.0), 50.0, Color::srgb(1.0, 0.0, 0.0));
            gizmos.circle_2d(Vec2::new(100.0, 100.0), 100.0, Color::srgb(1.0, 1.0, 0.0));
            gizmos.circle_2d(Vec2::new(100.0, 100.0), 150.0, Color::srgb(0.0, 1.0, 0.0));
        }

        // Draw decision tree visualization
        if debug_info.visualization.show_decision_tree {
            // Draw decision flow lines (simplified)
            gizmos.line_2d(Vec2::new(50.0, 200.0), Vec2::new(150.0, 200.0), Color::WHITE);
            gizmos.line_2d(Vec2::new(100.0, 180.0), Vec2::new(100.0, 220.0), Color::WHITE);
        }
    }
}

/// AI Debug Data Export System
pub fn ai_debug_data_export_system(
    debug_query: Query<&AIDebugInfo, With<Fighter>>,
    config: Res<AIDebugConfig>,
    time: Res<Time>,
    mut last_export: Local<u64>,
) {
    if !config.auto_export {
        return;
    }

    let current_frame = (time.elapsed_secs() * 60.0) as u64;

    // Check if it's time to export
    if current_frame - *last_export >= config.export_interval {
        *last_export = current_frame;

        for debug_info in debug_query.iter() {
            if debug_info.enabled {
                // Export debug data to JSON file
                if let Ok(json_data) = debug_info.export_to_json() {
                    let filename = format!("ai_debug_export_{}.json", current_frame);
                    if let Err(e) = std::fs::write(&filename, json_data) {
                        eprintln!("Failed to export AI debug data: {}", e);
                    } else {
                        println!("AI debug data exported to {}", filename);
                    }
                }

                // Generate and save debug report
                let report = debug_info.generate_debug_report();
                let report_filename = format!("ai_debug_report_{}.txt", current_frame);
                if let Err(e) = std::fs::write(&report_filename, report) {
                    eprintln!("Failed to export AI debug report: {}", e);
                } else {
                    println!("AI debug report exported to {}", report_filename);
                }
            }
        }
    }
}

/// AI Debug Console Commands System
pub fn ai_debug_console_system(
    mut debug_query: Query<&mut AIDebugInfo, With<Fighter>>,
    mut tuning_interface: ResMut<AITuningInterface>,
    mut config: ResMut<AIDebugConfig>,
    keyboard: Res<BevyButtonInput<KeyCode>>,
) {
    // Toggle debug mode with F1
    if keyboard.just_pressed(KeyCode::F1) {
        config.debug_enabled = !config.debug_enabled;
        println!("AI Debug: {}", if config.debug_enabled { "Enabled" } else { "Disabled" });
    }

    // Cycle debug level with F2
    if keyboard.just_pressed(KeyCode::F2) && config.debug_enabled {
        config.debug_level = (config.debug_level + 1) % 4;
        println!("AI Debug Level: {}", config.debug_level);
    }

    // Toggle visual debugging with F3
    if keyboard.just_pressed(KeyCode::F3) && config.debug_enabled {
        config.visual_debugging = !config.visual_debugging;
        println!("AI Visual Debug: {}", if config.visual_debugging { "Enabled" } else { "Disabled" });
    }

    // Toggle real-time tuning with F4
    if keyboard.just_pressed(KeyCode::F4) {
        tuning_interface.enabled = !tuning_interface.enabled;
        println!("AI Real-time Tuning: {}", if tuning_interface.enabled { "Enabled" } else { "Disabled" });
    }

    // Export debug data with F5
    if keyboard.just_pressed(KeyCode::F5) && config.debug_enabled {
        for debug_info in debug_query.iter() {
            if debug_info.enabled {
                let report = debug_info.generate_debug_report();
                println!("{}", report);

                if let Ok(json_data) = debug_info.export_to_json() {
                    let filename = format!("ai_debug_manual_export_{}.json",
                        std::time::SystemTime::now()
                            .duration_since(std::time::UNIX_EPOCH)
                            .unwrap()
                            .as_secs());
                    if let Err(e) = std::fs::write(&filename, json_data) {
                        eprintln!("Failed to export AI debug data: {}", e);
                    } else {
                        println!("AI debug data manually exported to {}", filename);
                    }
                }
            }
        }
    }

    // Clear debug history with F6
    if keyboard.just_pressed(KeyCode::F6) && config.debug_enabled {
        for mut debug_info in debug_query.iter_mut() {
            debug_info.clear_history();
            println!("AI debug history cleared");
        }
    }
}

// ============================================================================
// AI Debug Plugin
// ============================================================================

/// Plugin for AI Debug and Tuning System
pub struct AIDebugPlugin;

impl Plugin for AIDebugPlugin {
    fn build(&self, app: &mut App) {
        // Initialize resources from environment variables
        let debug_config = AIDebugConfig::from_env();
        let tuning_interface = AITuningInterface::from_env();

        app
            .insert_resource(debug_config)
            .insert_resource(tuning_interface)
            .add_systems(Update, (
                ai_debug_data_collection_system,
                ai_performance_monitoring_system,
                ai_real_time_tuning_system,
                ai_debug_visualization_system,
                ai_debug_data_export_system,
                ai_debug_console_system,
            ).chain());

        // Print debug configuration on startup
        if let Ok(_) = std::env::var("AI_DEBUG_ENABLED") {
            println!("=== AI Debug System Initialized ===");
            println!("Debug Level: {}", app.world().resource::<AIDebugConfig>().debug_level);
            println!("Performance Monitoring: {}", app.world().resource::<AIDebugConfig>().performance_monitoring);
            println!("Visual Debugging: {}", app.world().resource::<AIDebugConfig>().visual_debugging);
            println!("Real-time Tuning: {}", app.world().resource::<AITuningInterface>().enabled);
            println!("Auto Export: {}", app.world().resource::<AIDebugConfig>().auto_export);
            println!("=====================================");
        }
    }
}
