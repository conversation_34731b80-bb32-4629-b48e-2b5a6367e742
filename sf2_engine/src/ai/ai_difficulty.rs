// AI Difficulty System - adaptive difficulty scaling and dice roll mechanics
// Implements the sophisticated difficulty lookup tables and scaling from C99

use bevy::prelude::*;
use serde::{Deserialize, Serialize};
use sf2_types::*;
use sf2_types::character_traits::AIBehaviorPattern;
use crate::ai::ai_core::*;
use crate::components::Fighter;
use std::collections::HashMap;

/// AI Action Types for difficulty-based decision making
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq, Hash, Serialize, Deserialize)]
pub enum AIActionType {
    Block,
    Attack,
    SpecialMove,
    Jump,
    Throw,
    Retreat,
}

/// AI Performance Metrics for adaptive difficulty adjustment
#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct AIPerformanceMetrics {
    /// Hit ratio (hits landed / total attacks)
    pub hit_ratio: f32,
    /// Combo success rate
    pub combo_success_rate: f32,
    /// Average reaction time in frames
    pub average_reaction_time: f32,
    /// Special move usage frequency
    pub special_move_usage: f32,
    /// Defensive success rate
    pub defensive_success_rate: f32,
    /// Total frames analyzed
    pub total_frames: u64,
    /// Wins vs losses ratio
    pub win_loss_ratio: f32,
}

impl Default for AIPerformanceMetrics {
    fn default() -> Self {
        Self {
            hit_ratio: 0.5,
            combo_success_rate: 0.3,
            average_reaction_time: 15.0,
            special_move_usage: 0.2,
            defensive_success_rate: 0.4,
            total_frames: 0,
            win_loss_ratio: 0.5,
        }
    }
}

/// Difficulty lookup table data (matching C99 data_2c388)
/// These are bit patterns for dice roll probability at each difficulty level
const DIFFICULTY_DICE_PATTERNS: [u32; 32] = [
    0x00008000, 0x04000000, 0x00008000, 0x01000000, // 1 bit each
    0x04000200, 0x10004000, 0x00408000, 0x0000c000, // 2 bits each
    0x04040400, 0x02010100, 0x08002080, 0x40100400, // 3 bits each
    0x10020408, 0x81002040, 0x10040804, 0x80402040, // 4 bits each
    0x41010408, 0x04402084, 0x21040410, 0x44104202, // 5-6 bits
    0x210210a1, 0x11412450, 0x88245242, 0x8a490a24, // 7-9 bits
    0x44a49512, 0x22a492c9, 0x4d96aa28, 0xaaaaaaaa, // 11-16 bits
    0x7e5caa55, 0xaebabbae, 0xf56ede6b, 0x77777776, // 18-24 bits
];

/// AI Timer lookup table for different difficulty levels
const AI_TIMER_LOOKUP: [[u8; 4]; 32] = [
    [120, 120, 120, 120], [120, 120, 120, 120], [120, 120, 120, 120], [120, 120, 120, 120],
    [100, 100, 100, 100], [100, 100, 100, 100], [100, 100, 100, 100], [100, 100, 100, 100],
    [80, 80, 80, 80], [80, 80, 80, 80], [80, 80, 80, 80], [80, 80, 80, 80],
    [60, 60, 60, 60], [60, 60, 60, 60], [60, 60, 60, 60], [60, 60, 60, 60],
    [50, 50, 50, 50], [50, 50, 50, 50], [50, 50, 50, 50], [50, 50, 50, 50],
    [40, 40, 40, 40], [40, 40, 40, 40], [40, 40, 40, 40], [40, 40, 40, 40],
    [30, 30, 30, 30], [30, 30, 30, 30], [30, 30, 30, 30], [30, 30, 30, 30],
    [20, 20, 20, 20], [20, 20, 20, 20], [20, 20, 20, 20], [20, 20, 20, 20],
];

/// Difficulty scaling for damage (matching C99 data_93420)
const DAMAGE_SCALING: [u8; 32] = [
    0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x14, 0x14, 0x14, 0x14, 0x14, 0x10, 0x10, 0x10, 0x10, 0x10,
    0x0c, 0x0c, 0x0c, 0x0c, 0x0c, 0x08, 0x08, 0x08, 0x08, 0x08, 0x04, 0x04, 0x04, 0x04, 0x04, 0xc0,
];

/// Random difficulty adjustment table (matching C99 data_2e4e)
const RANDOM_DIFFICULTY_ADJUST: [u8; 32] = [
    0x00, 0x01, 0x01, 0x02, 0x02, 0x02, 0x02, 0x03,
    0x03, 0x03, 0x03, 0x03, 0x04, 0x04, 0x04, 0x04,
    0x04, 0x04, 0x04, 0x04, 0x05, 0x05, 0x05, 0x05,
    0x05, 0x06, 0x06, 0x06, 0x06, 0x07, 0x07, 0x08,
];

/// AI Difficulty Component for Bevy ECS
#[derive(Component, Debug, Clone, Serialize, Deserialize)]
pub struct AIDifficulty {
    /// Base difficulty level (0-31)
    pub base_difficulty: u8,
    /// Current effective difficulty (can be modified by game state)
    pub current_difficulty: u8,
    /// Character-specific difficulty modifier
    pub character_modifier: i8,
    /// Adaptive difficulty adjustment
    pub adaptive_adjustment: i8,
    /// Reaction time scaling (frames)
    pub reaction_time_frames: u16,
    /// Aggression scaling factor (0.0 to 2.0)
    pub aggression_scale: f32,
    /// Defense scaling factor (0.0 to 2.0)
    pub defense_scale: f32,
    /// Special move frequency scaling (0.0 to 2.0)
    pub special_frequency_scale: f32,
    /// Environment variable overrides
    pub env_overrides: HashMap<String, f32>,
}

impl Default for AIDifficulty {
    fn default() -> Self {
        Self {
            base_difficulty: 16, // Medium difficulty
            current_difficulty: 16,
            character_modifier: 0,
            adaptive_adjustment: 0,
            reaction_time_frames: 15, // Default reaction time
            aggression_scale: 1.0,
            defense_scale: 1.0,
            special_frequency_scale: 1.0,
            env_overrides: HashMap::new(),
        }
    }
}

impl AIDifficulty {
    /// Create new difficulty settings for a character
    pub fn new(base_difficulty: u8, character_id: FighterId) -> Self {
        let mut difficulty = Self::default();
        difficulty.base_difficulty = base_difficulty.min(31);
        difficulty.current_difficulty = difficulty.base_difficulty;
        
        // Apply character-specific modifiers
        difficulty.character_modifier = match character_id {
            FighterId::Ryu => 0,        // Balanced
            FighterId::Ken => 1,        // Slightly more aggressive
            FighterId::ChunLi => -1,    // Slightly more defensive
            FighterId::Guile => -2,     // More defensive (charge character)
            FighterId::Blanka => 2,     // More unpredictable
            FighterId::EHonda => 0,     // Balanced
            FighterId::Zangief => 1,    // More aggressive (grappler)
            FighterId::Dhalsim => -1,   // More defensive (long range)
            FighterId::Balrog => 2,     // Very aggressive (boxer)
            FighterId::Vega => 1,       // Aggressive (claw)
            FighterId::Sagat => -1,     // Defensive (tiger shots)
            FighterId::MBison => 0,     // Balanced (boss)
        };
        
        difficulty.update_scaling();
        difficulty
    }
    
    /// Update difficulty scaling factors based on current difficulty
    pub fn update_scaling(&mut self) {
        let effective_difficulty = (self.current_difficulty as i16 + self.character_modifier as i16 + self.adaptive_adjustment as i16)
            .max(0).min(31) as u8;
        
        // Calculate reaction time (higher difficulty = faster reaction)
        self.reaction_time_frames = match effective_difficulty {
            0..=7 => 30,    // Slow reaction
            8..=15 => 20,   // Medium reaction
            16..=23 => 15,  // Fast reaction
            24..=31 => 10,  // Very fast reaction
            _ => 5,         // Ultra fast reaction for 32+
        };
        
        // Calculate aggression scaling (higher difficulty = more aggressive)
        self.aggression_scale = 0.5 + (effective_difficulty as f32 / 31.0) * 1.5;
        
        // Calculate defense scaling (higher difficulty = better defense)
        self.defense_scale = 0.3 + (effective_difficulty as f32 / 31.0) * 1.7;
        
        // Calculate special move frequency (higher difficulty = more specials)
        self.special_frequency_scale = 0.2 + (effective_difficulty as f32 / 31.0) * 1.8;
        
        // Apply environment variable overrides
        if let Some(aggression_override) = self.env_overrides.get("AI_AGGRESSION_SCALE") {
            self.aggression_scale = *aggression_override;
        }
        if let Some(defense_override) = self.env_overrides.get("AI_DEFENSE_SCALE") {
            self.defense_scale = *defense_override;
        }
        if let Some(special_override) = self.env_overrides.get("AI_SPECIAL_FREQUENCY_SCALE") {
            self.special_frequency_scale = *special_override;
        }
        if let Some(reaction_override) = self.env_overrides.get("AI_REACTION_TIME_FRAMES") {
            self.reaction_time_frames = *reaction_override as u16;
        }
    }
    
    /// Perform dice roll check (matching C99 comp_diceroll)
    pub fn dice_roll(&self, random_value: u32) -> bool {
        let pattern = DIFFICULTY_DICE_PATTERNS[self.current_difficulty as usize];
        let bit_position = random_value & 0x1F; // Use lower 5 bits for bit position
        (pattern & (1 << bit_position)) != 0
    }
    
    /// Perform difficulty lookup (matching C99 comp_ply_difficulty_lookup)
    pub fn difficulty_lookup(&self, lookup_table: &[u32; 32], character_id: FighterId, random_value: u32) -> bool {
        let character_index = character_id as usize;
        if character_index < 12 { // Only 12 characters in original
            let pattern = lookup_table[self.current_difficulty as usize];
            let bit_position = random_value & 0x1F;
            (pattern & (1 << bit_position)) != 0
        } else {
            false
        }
    }
    
    /// Get AI timer value for current difficulty
    pub fn get_ai_timer(&self, timer_index: usize) -> u8 {
        if timer_index < 4 {
            AI_TIMER_LOOKUP[self.current_difficulty as usize][timer_index]
        } else {
            60 // Default timer value
        }
    }
    
    /// Get damage scaling for current difficulty
    pub fn get_damage_scaling(&self) -> f32 {
        DAMAGE_SCALING[self.current_difficulty as usize] as f32 / 24.0
    }
    
    /// Adjust difficulty adaptively based on performance
    pub fn adjust_adaptive_difficulty(&mut self, player_winning: bool, rounds_won_difference: i8) {
        if player_winning && rounds_won_difference > 0 {
            // Player is winning, increase AI difficulty
            self.adaptive_adjustment = (self.adaptive_adjustment + 1).min(8);
        } else if !player_winning && rounds_won_difference < 0 {
            // AI is winning, decrease AI difficulty slightly
            self.adaptive_adjustment = (self.adaptive_adjustment - 1).max(-4);
        }

        // Apply random adjustment (matching C99 logic)
        let random_adjust = RANDOM_DIFFICULTY_ADJUST[(fastrand::u32(..) & 0x1F) as usize];
        self.adaptive_adjustment = (self.adaptive_adjustment + random_adjust as i8).min(8);

        self.update_scaling();
    }

    /// Advanced adaptive difficulty adjustment based on detailed performance metrics
    pub fn adjust_adaptive_difficulty_advanced(&mut self, performance_metrics: &AIPerformanceMetrics) {
        let mut adjustment = 0i8;

        // Analyze hit ratio - if AI is hitting too much, reduce difficulty
        if performance_metrics.hit_ratio > 0.7 {
            adjustment -= 2;
        } else if performance_metrics.hit_ratio < 0.3 {
            adjustment += 1;
        }

        // Analyze combo success rate
        if performance_metrics.combo_success_rate > 0.8 {
            adjustment -= 1;
        } else if performance_metrics.combo_success_rate < 0.2 {
            adjustment += 1;
        }

        // Analyze reaction time performance
        if performance_metrics.average_reaction_time < 5.0 {
            adjustment -= 1; // AI reacting too fast
        } else if performance_metrics.average_reaction_time > 30.0 {
            adjustment += 1; // AI reacting too slow
        }

        // Analyze special move usage
        if performance_metrics.special_move_usage > 0.6 {
            adjustment -= 1; // Using too many specials
        } else if performance_metrics.special_move_usage < 0.1 {
            adjustment += 1; // Not using enough specials
        }

        // Apply the calculated adjustment
        self.adaptive_adjustment = (self.adaptive_adjustment + adjustment).clamp(-8, 8);
        self.update_scaling();
    }



    /// Check if AI should perform a specific action based on difficulty
    pub fn should_perform_action(&self, action_type: AIActionType, random_value: u32) -> bool {
        let threshold = match action_type {
            AIActionType::Block => self.defense_scale * 0.5,
            AIActionType::Attack => self.aggression_scale * 0.4,
            AIActionType::SpecialMove => self.special_frequency_scale * 0.3,
            AIActionType::Jump => 0.3, // Base jump frequency
            AIActionType::Throw => self.aggression_scale * 0.2,
            AIActionType::Retreat => self.defense_scale * 0.4,
        };

        let normalized_random = (random_value & 0xFF) as f32 / 255.0;
        normalized_random < threshold
    }
    
    /// Set difficulty from environment variables
    pub fn apply_env_overrides(&mut self, env_vars: &HashMap<String, String>) {
        for (key, value) in env_vars {
            match key.as_str() {
                "AI_BASE_DIFFICULTY" => {
                    if let Ok(difficulty) = value.parse::<u8>() {
                        self.base_difficulty = difficulty.min(31);
                        self.current_difficulty = self.base_difficulty;
                    }
                }
                "AI_AGGRESSION_SCALE" => {
                    if let Ok(scale) = value.parse::<f32>() {
                        self.env_overrides.insert(key.clone(), scale.max(0.0).min(3.0));
                    }
                }
                "AI_DEFENSE_SCALE" => {
                    if let Ok(scale) = value.parse::<f32>() {
                        self.env_overrides.insert(key.clone(), scale.max(0.0).min(3.0));
                    }
                }
                "AI_SPECIAL_FREQUENCY_SCALE" => {
                    if let Ok(scale) = value.parse::<f32>() {
                        self.env_overrides.insert(key.clone(), scale.max(0.0).min(3.0));
                    }
                }
                "AI_REACTION_TIME_FRAMES" => {
                    if let Ok(frames) = value.parse::<f32>() {
                        self.env_overrides.insert(key.clone(), frames.max(1.0).min(120.0));
                    }
                }
                _ => {}
            }
        }
        self.update_scaling();
    }
    
    /// Scale AI behavior pattern based on difficulty
    pub fn scale_behavior_pattern(&self, base_pattern: &AIBehaviorPattern) -> AIBehaviorPattern {
        AIBehaviorPattern {
            aggression: (base_pattern.aggression * self.aggression_scale).min(1.0),
            defense: (base_pattern.defense * self.defense_scale).min(1.0),
            special_move_frequency: (base_pattern.special_move_frequency * self.special_frequency_scale).min(1.0),
            jump_frequency: base_pattern.jump_frequency, // Keep jump frequency unchanged for now
            preferred_distance: base_pattern.preferred_distance,
            reaction_time: self.reaction_time_frames,
        }
    }
}

/// AI Difficulty Resource for global difficulty management
#[derive(Resource, Debug, Clone, Serialize, Deserialize)]
pub struct AIDifficultyResource {
    /// Global difficulty settings
    pub global_difficulty: u8,
    /// Demo mode difficulty
    pub demo_difficulty: u8,
    /// Two-player mode difficulty balancing
    pub two_player_balancing: bool,
    /// Difficulty progression settings
    pub progression_enabled: bool,
    /// Environment variable cache
    pub env_cache: HashMap<String, String>,
}

impl Default for AIDifficultyResource {
    fn default() -> Self {
        Self {
            global_difficulty: 16,
            demo_difficulty: 15,
            two_player_balancing: true,
            progression_enabled: true,
            env_cache: HashMap::new(),
        }
    }
}

impl AIDifficultyResource {
    /// Initialize difficulty from game settings
    pub fn init_from_game_state(&mut self, game_state: &GameState) {
        // Set difficulty based on game mode
        match game_state.game_mode {
            GameMode::AttractMode => {
                self.global_difficulty = self.demo_difficulty;
            }
            _ => {
                // Use configured difficulty
            }
        }
        
        // Load environment variables
        self.load_env_variables();
    }
    
    /// Load environment variables for AI configuration
    fn load_env_variables(&mut self) {
        use std::env;
        
        let ai_env_vars = [
            "AI_BASE_DIFFICULTY",
            "AI_AGGRESSION_SCALE", 
            "AI_DEFENSE_SCALE",
            "AI_SPECIAL_FREQUENCY_SCALE",
            "AI_REACTION_TIME_FRAMES",
            "AI_ADAPTIVE_ENABLED",
            "AI_DEBUG_MODE",
        ];
        
        for var_name in &ai_env_vars {
            if let Ok(value) = env::var(var_name) {
                self.env_cache.insert(var_name.to_string(), value);
            }
        }
    }
    
    /// Create difficulty settings for a new AI player
    pub fn create_player_difficulty(&self, character_id: FighterId, is_demo: bool) -> AIDifficulty {
        let base_difficulty = if is_demo {
            self.demo_difficulty
        } else {
            self.global_difficulty
        };
        
        let mut difficulty = AIDifficulty::new(base_difficulty, character_id);
        difficulty.apply_env_overrides(&self.env_cache);
        difficulty
    }
}

/// AI Difficulty Adaptation System for Bevy ECS
pub fn ai_difficulty_adaptation_system(
    mut difficulty_query: Query<&mut AIDifficulty, With<AICore>>,
    fighter_query: Query<&FighterStateData, With<Fighter>>,
    time: Res<Time>,
    performance_tracker: Local<HashMap<Entity, AIPerformanceMetrics>>,
) {
    for mut difficulty in difficulty_query.iter_mut() {
        // Update difficulty scaling every 60 frames (1 second at 60 FPS)
        if time.elapsed_secs() as u64 % 60 == 0 {
            difficulty.update_scaling();
        }
    }
}

/// AI Performance Tracking System
pub fn ai_performance_tracking_system(
    mut performance_tracker: Local<HashMap<Entity, AIPerformanceMetrics>>,
    ai_query: Query<(Entity, &AICore), With<Fighter>>,
    fighter_query: Query<&FighterStateData, With<Fighter>>,
    mut difficulty_query: Query<&mut AIDifficulty>,
) {
    for (entity, ai_core) in ai_query.iter() {
        let metrics = performance_tracker.entry(entity).or_insert_with(|| AIPerformanceMetrics {
            hit_ratio: 0.5,
            combo_success_rate: 0.3,
            average_reaction_time: 15.0,
            special_move_usage: 0.2,
            defensive_success_rate: 0.4,
            total_frames: 0,
            win_loss_ratio: 0.5,
        });

        metrics.total_frames += 1;

        // Update metrics based on AI state
        // This would be enhanced with actual game state analysis
        if ai_core.control_signals.do_block {
            metrics.defensive_success_rate = (metrics.defensive_success_rate * 0.99 + 0.01).min(1.0);
        }

        // Adjust difficulty every 300 frames (5 seconds)
        if metrics.total_frames % 300 == 0 {
            if let Ok(mut difficulty) = difficulty_query.get_mut(entity) {
                difficulty.adjust_adaptive_difficulty_advanced(metrics);
            }
        }
    }
}

/// AI Difficulty Environment Variable Update System
pub fn ai_difficulty_env_update_system(
    mut difficulty_resource: ResMut<AIDifficultyResource>,
    mut difficulty_query: Query<&mut AIDifficulty>,
) {
    // Check for environment variable changes every 120 frames (2 seconds)
    static mut FRAME_COUNTER: u64 = 0;
    unsafe {
        FRAME_COUNTER += 1;
        if FRAME_COUNTER % 120 == 0 {
            difficulty_resource.load_env_variables();

            // Apply changes to all AI entities
            for mut difficulty in difficulty_query.iter_mut() {
                difficulty.apply_env_overrides(&difficulty_resource.env_cache);
            }
        }
    }
}
