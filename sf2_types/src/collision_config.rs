//! # Collision Configuration
//!
//! Environment variable configuration for collision detection system.
//! Provides granular adjustment of hitboxes, hurtboxes, and collision behavior.

use std::env;
use serde::{Deserialize, Serialize};

/// Configuration for collision detection system behavior
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct CollisionConfig {
    // Hitbox Configuration
    /// Global hitbox size multiplier (0.1 to 3.0)
    pub hitbox_size_multiplier: f32,
    /// Global hitbox damage multiplier (0.1 to 5.0)
    pub hitbox_damage_multiplier: f32,
    /// Hitbox position offset X (pixels)
    pub hitbox_offset_x: i16,
    /// Hitbox position offset Y (pixels)
    pub hitbox_offset_y: i16,
    /// Hitbox active frame extension (frames)
    pub hitbox_frame_extension: i16,
    
    // Hurtbox Configuration
    /// Global hurtbox size multiplier (0.1 to 3.0)
    pub hurtbox_size_multiplier: f32,
    /// Global hurtbox vulnerability multiplier (0.1 to 5.0)
    pub hurtbox_vulnerability_multiplier: f32,
    /// Hurtbox position offset X (pixels)
    pub hurtbox_offset_x: i16,
    /// Hurtbox position offset Y (pixels)
    pub hurtbox_offset_y: i16,
    
    // Pushbox Configuration
    /// Global pushbox size multiplier (0.1 to 3.0)
    pub pushbox_size_multiplier: f32,
    /// Pushbox position offset X (pixels)
    pub pushbox_offset_x: i16,
    /// Pushbox position offset Y (pixels)
    pub pushbox_offset_y: i16,
    
    // Fighter-Specific Hitbox Adjustments
    /// Ryu hitbox size multiplier
    pub ryu_hitbox_multiplier: f32,
    /// Ken hitbox size multiplier
    pub ken_hitbox_multiplier: f32,
    /// Chun-Li hitbox size multiplier
    pub chunli_hitbox_multiplier: f32,
    /// Blanka hitbox size multiplier
    pub blanka_hitbox_multiplier: f32,
    /// E.Honda hitbox size multiplier
    pub honda_hitbox_multiplier: f32,
    /// Zangief hitbox size multiplier
    pub zangief_hitbox_multiplier: f32,
    /// Guile hitbox size multiplier
    pub guile_hitbox_multiplier: f32,
    /// Dhalsim hitbox size multiplier
    pub dhalsim_hitbox_multiplier: f32,
    
    // Fighter-Specific Hurtbox Adjustments
    /// Ryu hurtbox size multiplier
    pub ryu_hurtbox_multiplier: f32,
    /// Ken hurtbox size multiplier
    pub ken_hurtbox_multiplier: f32,
    /// Chun-Li hurtbox size multiplier
    pub chunli_hurtbox_multiplier: f32,
    /// Blanka hurtbox size multiplier
    pub blanka_hurtbox_multiplier: f32,
    /// E.Honda hurtbox size multiplier
    pub honda_hurtbox_multiplier: f32,
    /// Zangief hurtbox size multiplier
    pub zangief_hurtbox_multiplier: f32,
    /// Guile hurtbox size multiplier
    pub guile_hurtbox_multiplier: f32,
    /// Dhalsim hurtbox size multiplier
    pub dhalsim_hurtbox_multiplier: f32,
    
    // Collision Detection Behavior
    /// Enable collision detection (for debugging)
    pub collision_enabled: bool,
    /// Collision detection precision (0=fast, 1=normal, 2=precise)
    pub collision_precision: u8,
    /// Enable spatial partitioning optimization
    pub spatial_partitioning_enabled: bool,
    /// Spatial grid cell size (pixels)
    pub spatial_grid_size: u16,
    /// Maximum collision checks per frame
    pub max_collision_checks_per_frame: u32,
    
    // Debug and Visualization
    /// Enable collision box visualization
    pub debug_collision_boxes: bool,
    /// Enable collision detection logging
    pub debug_collision_detection: bool,
    /// Enable performance profiling for collision
    pub debug_collision_performance: bool,
    /// Collision box visualization alpha (0.0 to 1.0)
    pub debug_box_alpha: f32,
    
    // Performance Tuning
    /// Enable SIMD optimizations for collision
    pub enable_simd_collision: bool,
    /// Collision batch processing size
    pub collision_batch_size: usize,
    /// Early exit threshold for broad-phase collision
    pub broad_phase_threshold: f32,
}

impl Default for CollisionConfig {
    fn default() -> Self {
        Self {
            // Hitbox defaults
            hitbox_size_multiplier: 1.0,
            hitbox_damage_multiplier: 1.0,
            hitbox_offset_x: 0,
            hitbox_offset_y: 0,
            hitbox_frame_extension: 0,
            
            // Hurtbox defaults
            hurtbox_size_multiplier: 1.0,
            hurtbox_vulnerability_multiplier: 1.0,
            hurtbox_offset_x: 0,
            hurtbox_offset_y: 0,
            
            // Pushbox defaults
            pushbox_size_multiplier: 1.0,
            pushbox_offset_x: 0,
            pushbox_offset_y: 0,
            
            // Fighter-specific hitbox defaults (all 1.0)
            ryu_hitbox_multiplier: 1.0,
            ken_hitbox_multiplier: 1.0,
            chunli_hitbox_multiplier: 1.0,
            blanka_hitbox_multiplier: 1.0,
            honda_hitbox_multiplier: 1.0,
            zangief_hitbox_multiplier: 1.0,
            guile_hitbox_multiplier: 1.0,
            dhalsim_hitbox_multiplier: 1.0,
            
            // Fighter-specific hurtbox defaults (all 1.0)
            ryu_hurtbox_multiplier: 1.0,
            ken_hurtbox_multiplier: 1.0,
            chunli_hurtbox_multiplier: 1.0,
            blanka_hurtbox_multiplier: 1.0,
            honda_hurtbox_multiplier: 1.0,
            zangief_hurtbox_multiplier: 1.0,
            guile_hurtbox_multiplier: 1.0,
            dhalsim_hurtbox_multiplier: 1.0,
            
            // Collision behavior defaults
            collision_enabled: true,
            collision_precision: 1,
            spatial_partitioning_enabled: true,
            spatial_grid_size: 64,
            max_collision_checks_per_frame: 1000,
            
            // Debug defaults
            debug_collision_boxes: false,
            debug_collision_detection: false,
            debug_collision_performance: false,
            debug_box_alpha: 0.5,
            
            // Performance defaults
            enable_simd_collision: true,
            collision_batch_size: 16,
            broad_phase_threshold: 100.0,
        }
    }
}

impl CollisionConfig {
    /// Load configuration from environment variables
    pub fn from_env() -> Self {
        let mut config = Self::default();
        
        // Global hitbox configuration
        if let Ok(val) = env::var("SF2_HITBOX_SIZE_MULTIPLIER") {
            if let Ok(multiplier) = val.parse::<f32>() {
                config.hitbox_size_multiplier = multiplier.clamp(0.1, 3.0);
            }
        }
        
        if let Ok(val) = env::var("SF2_HITBOX_DAMAGE_MULTIPLIER") {
            if let Ok(multiplier) = val.parse::<f32>() {
                config.hitbox_damage_multiplier = multiplier.clamp(0.1, 5.0);
            }
        }
        
        if let Ok(val) = env::var("SF2_HITBOX_OFFSET_X") {
            if let Ok(offset) = val.parse::<i16>() {
                config.hitbox_offset_x = offset.clamp(-100, 100);
            }
        }
        
        if let Ok(val) = env::var("SF2_HITBOX_OFFSET_Y") {
            if let Ok(offset) = val.parse::<i16>() {
                config.hitbox_offset_y = offset.clamp(-100, 100);
            }
        }
        
        if let Ok(val) = env::var("SF2_HITBOX_FRAME_EXTENSION") {
            if let Ok(extension) = val.parse::<i16>() {
                config.hitbox_frame_extension = extension.clamp(-10, 20);
            }
        }
        
        // Global hurtbox configuration
        if let Ok(val) = env::var("SF2_HURTBOX_SIZE_MULTIPLIER") {
            if let Ok(multiplier) = val.parse::<f32>() {
                config.hurtbox_size_multiplier = multiplier.clamp(0.1, 3.0);
            }
        }
        
        if let Ok(val) = env::var("SF2_HURTBOX_VULNERABILITY_MULTIPLIER") {
            if let Ok(multiplier) = val.parse::<f32>() {
                config.hurtbox_vulnerability_multiplier = multiplier.clamp(0.1, 5.0);
            }
        }
        
        if let Ok(val) = env::var("SF2_HURTBOX_OFFSET_X") {
            if let Ok(offset) = val.parse::<i16>() {
                config.hurtbox_offset_x = offset.clamp(-100, 100);
            }
        }
        
        if let Ok(val) = env::var("SF2_HURTBOX_OFFSET_Y") {
            if let Ok(offset) = val.parse::<i16>() {
                config.hurtbox_offset_y = offset.clamp(-100, 100);
            }
        }
        
        // Global pushbox configuration
        if let Ok(val) = env::var("SF2_PUSHBOX_SIZE_MULTIPLIER") {
            if let Ok(multiplier) = val.parse::<f32>() {
                config.pushbox_size_multiplier = multiplier.clamp(0.1, 3.0);
            }
        }
        
        if let Ok(val) = env::var("SF2_PUSHBOX_OFFSET_X") {
            if let Ok(offset) = val.parse::<i16>() {
                config.pushbox_offset_x = offset.clamp(-100, 100);
            }
        }
        
        if let Ok(val) = env::var("SF2_PUSHBOX_OFFSET_Y") {
            if let Ok(offset) = val.parse::<i16>() {
                config.pushbox_offset_y = offset.clamp(-100, 100);
            }
        }
        
        // Fighter-specific hitbox configuration
        Self::load_fighter_hitbox_config(&mut config);

        // Fighter-specific hurtbox configuration
        Self::load_fighter_hurtbox_config(&mut config);

        // Collision behavior configuration
        Self::load_collision_behavior_config(&mut config);

        // Debug configuration
        Self::load_debug_config(&mut config);

        // Performance configuration
        Self::load_performance_config(&mut config);

        config
    }

    /// Load fighter-specific hitbox configuration
    fn load_fighter_hitbox_config(config: &mut CollisionConfig) {
        let fighters = [
            ("RYU", &mut config.ryu_hitbox_multiplier),
            ("KEN", &mut config.ken_hitbox_multiplier),
            ("CHUNLI", &mut config.chunli_hitbox_multiplier),
            ("BLANKA", &mut config.blanka_hitbox_multiplier),
            ("HONDA", &mut config.honda_hitbox_multiplier),
            ("ZANGIEF", &mut config.zangief_hitbox_multiplier),
            ("GUILE", &mut config.guile_hitbox_multiplier),
            ("DHALSIM", &mut config.dhalsim_hitbox_multiplier),
        ];

        for (fighter_name, multiplier) in fighters {
            if let Ok(val) = env::var(&format!("SF2_{}_HITBOX_MULTIPLIER", fighter_name)) {
                if let Ok(mult) = val.parse::<f32>() {
                    *multiplier = mult.clamp(0.1, 3.0);
                }
            }
        }
    }

    /// Load fighter-specific hurtbox configuration
    fn load_fighter_hurtbox_config(config: &mut CollisionConfig) {
        let fighters = [
            ("RYU", &mut config.ryu_hurtbox_multiplier),
            ("KEN", &mut config.ken_hurtbox_multiplier),
            ("CHUNLI", &mut config.chunli_hurtbox_multiplier),
            ("BLANKA", &mut config.blanka_hurtbox_multiplier),
            ("HONDA", &mut config.honda_hurtbox_multiplier),
            ("ZANGIEF", &mut config.zangief_hurtbox_multiplier),
            ("GUILE", &mut config.guile_hurtbox_multiplier),
            ("DHALSIM", &mut config.dhalsim_hurtbox_multiplier),
        ];

        for (fighter_name, multiplier) in fighters {
            if let Ok(val) = env::var(&format!("SF2_{}_HURTBOX_MULTIPLIER", fighter_name)) {
                if let Ok(mult) = val.parse::<f32>() {
                    *multiplier = mult.clamp(0.1, 3.0);
                }
            }
        }
    }

    /// Load collision behavior configuration
    fn load_collision_behavior_config(config: &mut CollisionConfig) {
        if let Ok(val) = env::var("SF2_COLLISION_ENABLED") {
            config.collision_enabled = val.to_lowercase() == "true" || val == "1";
        }

        if let Ok(val) = env::var("SF2_COLLISION_PRECISION") {
            if let Ok(precision) = val.parse::<u8>() {
                config.collision_precision = precision.clamp(0, 2);
            }
        }

        if let Ok(val) = env::var("SF2_SPATIAL_PARTITIONING_ENABLED") {
            config.spatial_partitioning_enabled = val.to_lowercase() == "true" || val == "1";
        }

        if let Ok(val) = env::var("SF2_SPATIAL_GRID_SIZE") {
            if let Ok(size) = val.parse::<u16>() {
                config.spatial_grid_size = size.clamp(16, 512);
            }
        }

        if let Ok(val) = env::var("SF2_MAX_COLLISION_CHECKS_PER_FRAME") {
            if let Ok(max_checks) = val.parse::<u32>() {
                config.max_collision_checks_per_frame = max_checks.clamp(100, 10000);
            }
        }
    }

    /// Load debug configuration
    fn load_debug_config(config: &mut CollisionConfig) {
        if let Ok(val) = env::var("SF2_DEBUG_COLLISION_BOXES") {
            config.debug_collision_boxes = val.to_lowercase() == "true" || val == "1";
        }

        if let Ok(val) = env::var("SF2_DEBUG_COLLISION_DETECTION") {
            config.debug_collision_detection = val.to_lowercase() == "true" || val == "1";
        }

        if let Ok(val) = env::var("SF2_DEBUG_COLLISION_PERFORMANCE") {
            config.debug_collision_performance = val.to_lowercase() == "true" || val == "1";
        }

        if let Ok(val) = env::var("SF2_DEBUG_BOX_ALPHA") {
            if let Ok(alpha) = val.parse::<f32>() {
                config.debug_box_alpha = alpha.clamp(0.0, 1.0);
            }
        }
    }

    /// Load performance configuration
    fn load_performance_config(config: &mut CollisionConfig) {
        if let Ok(val) = env::var("SF2_ENABLE_SIMD_COLLISION") {
            config.enable_simd_collision = val.to_lowercase() == "true" || val == "1";
        }

        if let Ok(val) = env::var("SF2_COLLISION_BATCH_SIZE") {
            if let Ok(size) = val.parse::<usize>() {
                config.collision_batch_size = size.clamp(4, 128);
            }
        }

        if let Ok(val) = env::var("SF2_BROAD_PHASE_THRESHOLD") {
            if let Ok(threshold) = val.parse::<f32>() {
                config.broad_phase_threshold = threshold.clamp(10.0, 1000.0);
            }
        }
    }

    /// Get fighter-specific hitbox multiplier
    pub fn get_fighter_hitbox_multiplier(&self, fighter_id: u8) -> f32 {
        match fighter_id {
            0 => self.ryu_hitbox_multiplier,
            1 => self.ken_hitbox_multiplier,
            2 => self.chunli_hitbox_multiplier,
            3 => self.blanka_hitbox_multiplier,
            4 => self.honda_hitbox_multiplier,
            5 => self.zangief_hitbox_multiplier,
            6 => self.guile_hitbox_multiplier,
            7 => self.dhalsim_hitbox_multiplier,
            _ => 1.0, // Default for unknown fighters
        }
    }

    /// Get fighter-specific hurtbox multiplier
    pub fn get_fighter_hurtbox_multiplier(&self, fighter_id: u8) -> f32 {
        match fighter_id {
            0 => self.ryu_hurtbox_multiplier,
            1 => self.ken_hurtbox_multiplier,
            2 => self.chunli_hurtbox_multiplier,
            3 => self.blanka_hurtbox_multiplier,
            4 => self.honda_hurtbox_multiplier,
            5 => self.zangief_hurtbox_multiplier,
            6 => self.guile_hurtbox_multiplier,
            7 => self.dhalsim_hurtbox_multiplier,
            _ => 1.0, // Default for unknown fighters
        }
    }

    /// Apply configuration to hitbox dimensions
    pub fn apply_hitbox_config(&self, fighter_id: u8, width: &mut i8, height: &mut i8, x: &mut i8, y: &mut i8) {
        let global_multiplier = self.hitbox_size_multiplier;
        let fighter_multiplier = self.get_fighter_hitbox_multiplier(fighter_id);
        let total_multiplier = global_multiplier * fighter_multiplier;

        // Apply size multipliers
        *width = (*width as f32 * total_multiplier) as i8;
        *height = (*height as f32 * total_multiplier) as i8;

        // Apply position offsets
        *x = x.saturating_add(self.hitbox_offset_x as i8);
        *y = y.saturating_add(self.hitbox_offset_y as i8);
    }

    /// Apply configuration to hurtbox dimensions
    pub fn apply_hurtbox_config(&self, fighter_id: u8, width: &mut i8, height: &mut i8, x: &mut i8, y: &mut i8) {
        let global_multiplier = self.hurtbox_size_multiplier;
        let fighter_multiplier = self.get_fighter_hurtbox_multiplier(fighter_id);
        let total_multiplier = global_multiplier * fighter_multiplier;

        // Apply size multipliers
        *width = (*width as f32 * total_multiplier) as i8;
        *height = (*height as f32 * total_multiplier) as i8;

        // Apply position offsets
        *x = x.saturating_add(self.hurtbox_offset_x as i8);
        *y = y.saturating_add(self.hurtbox_offset_y as i8);
    }

    /// Apply configuration to pushbox dimensions
    pub fn apply_pushbox_config(&self, width: &mut i8, height: &mut i8, x: &mut i8, y: &mut i8) {
        let multiplier = self.pushbox_size_multiplier;

        // Apply size multiplier
        *width = (*width as f32 * multiplier) as i8;
        *height = (*height as f32 * multiplier) as i8;

        // Apply position offsets
        *x = x.saturating_add(self.pushbox_offset_x as i8);
        *y = y.saturating_add(self.pushbox_offset_y as i8);
    }

    /// Print current configuration for debugging
    pub fn print_config(&self) {
        println!("=== SF2 Collision Configuration ===");
        println!("Global Hitbox:");
        println!("  Size Multiplier: {}", self.hitbox_size_multiplier);
        println!("  Damage Multiplier: {}", self.hitbox_damage_multiplier);
        println!("  Offset: ({}, {})", self.hitbox_offset_x, self.hitbox_offset_y);
        println!("  Frame Extension: {}", self.hitbox_frame_extension);

        println!("Global Hurtbox:");
        println!("  Size Multiplier: {}", self.hurtbox_size_multiplier);
        println!("  Vulnerability Multiplier: {}", self.hurtbox_vulnerability_multiplier);
        println!("  Offset: ({}, {})", self.hurtbox_offset_x, self.hurtbox_offset_y);

        println!("Global Pushbox:");
        println!("  Size Multiplier: {}", self.pushbox_size_multiplier);
        println!("  Offset: ({}, {})", self.pushbox_offset_x, self.pushbox_offset_y);

        println!("Collision Behavior:");
        println!("  Enabled: {}", self.collision_enabled);
        println!("  Precision: {}", self.collision_precision);
        println!("  Spatial Partitioning: {}", self.spatial_partitioning_enabled);
        println!("  Grid Size: {}", self.spatial_grid_size);
        println!("  Max Checks/Frame: {}", self.max_collision_checks_per_frame);

        println!("Debug Settings:");
        println!("  Show Collision Boxes: {}", self.debug_collision_boxes);
        println!("  Debug Detection: {}", self.debug_collision_detection);
        println!("  Debug Performance: {}", self.debug_collision_performance);
        println!("  Box Alpha: {}", self.debug_box_alpha);

        println!("Performance:");
        println!("  SIMD Enabled: {}", self.enable_simd_collision);
        println!("  Batch Size: {}", self.collision_batch_size);
        println!("  Broad Phase Threshold: {}", self.broad_phase_threshold);
    }
}

/// Environment variable documentation for collision system
pub const COLLISION_ENV_VAR_DOCS: &str = r#"
SF2 Collision System Environment Variables:

Global Hitbox Configuration:
  SF2_HITBOX_SIZE_MULTIPLIER          - Global hitbox size multiplier (0.1-3.0, default: 1.0)
  SF2_HITBOX_DAMAGE_MULTIPLIER        - Global hitbox damage multiplier (0.1-5.0, default: 1.0)
  SF2_HITBOX_OFFSET_X                 - Global hitbox X offset in pixels (-100 to 100, default: 0)
  SF2_HITBOX_OFFSET_Y                 - Global hitbox Y offset in pixels (-100 to 100, default: 0)
  SF2_HITBOX_FRAME_EXTENSION          - Extend hitbox active frames (-10 to 20, default: 0)

Global Hurtbox Configuration:
  SF2_HURTBOX_SIZE_MULTIPLIER         - Global hurtbox size multiplier (0.1-3.0, default: 1.0)
  SF2_HURTBOX_VULNERABILITY_MULTIPLIER - Global vulnerability multiplier (0.1-5.0, default: 1.0)
  SF2_HURTBOX_OFFSET_X                - Global hurtbox X offset in pixels (-100 to 100, default: 0)
  SF2_HURTBOX_OFFSET_Y                - Global hurtbox Y offset in pixels (-100 to 100, default: 0)

Global Pushbox Configuration:
  SF2_PUSHBOX_SIZE_MULTIPLIER         - Global pushbox size multiplier (0.1-3.0, default: 1.0)
  SF2_PUSHBOX_OFFSET_X                - Global pushbox X offset in pixels (-100 to 100, default: 0)
  SF2_PUSHBOX_OFFSET_Y                - Global pushbox Y offset in pixels (-100 to 100, default: 0)

Fighter-Specific Hitbox Multipliers:
  SF2_RYU_HITBOX_MULTIPLIER           - Ryu hitbox size multiplier (0.1-3.0, default: 1.0)
  SF2_KEN_HITBOX_MULTIPLIER           - Ken hitbox size multiplier (0.1-3.0, default: 1.0)
  SF2_CHUNLI_HITBOX_MULTIPLIER        - Chun-Li hitbox size multiplier (0.1-3.0, default: 1.0)
  SF2_BLANKA_HITBOX_MULTIPLIER        - Blanka hitbox size multiplier (0.1-3.0, default: 1.0)
  SF2_HONDA_HITBOX_MULTIPLIER         - E.Honda hitbox size multiplier (0.1-3.0, default: 1.0)
  SF2_ZANGIEF_HITBOX_MULTIPLIER       - Zangief hitbox size multiplier (0.1-3.0, default: 1.0)
  SF2_GUILE_HITBOX_MULTIPLIER         - Guile hitbox size multiplier (0.1-3.0, default: 1.0)
  SF2_DHALSIM_HITBOX_MULTIPLIER       - Dhalsim hitbox size multiplier (0.1-3.0, default: 1.0)

Fighter-Specific Hurtbox Multipliers:
  SF2_RYU_HURTBOX_MULTIPLIER          - Ryu hurtbox size multiplier (0.1-3.0, default: 1.0)
  SF2_KEN_HURTBOX_MULTIPLIER          - Ken hurtbox size multiplier (0.1-3.0, default: 1.0)
  SF2_CHUNLI_HURTBOX_MULTIPLIER       - Chun-Li hurtbox size multiplier (0.1-3.0, default: 1.0)
  SF2_BLANKA_HURTBOX_MULTIPLIER       - Blanka hurtbox size multiplier (0.1-3.0, default: 1.0)
  SF2_HONDA_HURTBOX_MULTIPLIER        - E.Honda hurtbox size multiplier (0.1-3.0, default: 1.0)
  SF2_ZANGIEF_HURTBOX_MULTIPLIER      - Zangief hurtbox size multiplier (0.1-3.0, default: 1.0)
  SF2_GUILE_HURTBOX_MULTIPLIER        - Guile hurtbox size multiplier (0.1-3.0, default: 1.0)
  SF2_DHALSIM_HURTBOX_MULTIPLIER      - Dhalsim hurtbox size multiplier (0.1-3.0, default: 1.0)

Collision Detection Behavior:
  SF2_COLLISION_ENABLED               - Enable/disable collision detection (true/false, default: true)
  SF2_COLLISION_PRECISION             - Collision precision level (0-2, default: 1)
  SF2_SPATIAL_PARTITIONING_ENABLED    - Enable spatial partitioning (true/false, default: true)
  SF2_SPATIAL_GRID_SIZE               - Spatial grid cell size in pixels (16-512, default: 64)
  SF2_MAX_COLLISION_CHECKS_PER_FRAME  - Max collision checks per frame (100-10000, default: 1000)

Debug and Visualization:
  SF2_DEBUG_COLLISION_BOXES           - Show collision boxes (true/false, default: false)
  SF2_DEBUG_COLLISION_DETECTION       - Enable collision logging (true/false, default: false)
  SF2_DEBUG_COLLISION_PERFORMANCE     - Enable performance profiling (true/false, default: false)
  SF2_DEBUG_BOX_ALPHA                 - Collision box transparency (0.0-1.0, default: 0.5)

Performance Tuning:
  SF2_ENABLE_SIMD_COLLISION           - Enable SIMD optimizations (true/false, default: true)
  SF2_COLLISION_BATCH_SIZE            - Batch processing size (4-128, default: 16)
  SF2_BROAD_PHASE_THRESHOLD           - Broad-phase distance threshold (10.0-1000.0, default: 100.0)

Examples:
  export SF2_HITBOX_SIZE_MULTIPLIER=1.2     # Make all hitboxes 20% larger
  export SF2_RYU_HURTBOX_MULTIPLIER=0.9     # Make Ryu's hurtbox 10% smaller
  export SF2_DEBUG_COLLISION_BOXES=true     # Show collision boxes for debugging
  export SF2_COLLISION_PRECISION=2          # Use highest precision collision detection
"#;
