//! # Input Types
//!
//! Input handling and special move detection types.
//! Provides C99-compatible input structures and modern Rust input processing.

use serde::{Deserialize, Serialize};
use crate::constants::FbDirection;

/// Input directions
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>q, Eq, <PERSON>h, Serialize, Deserialize)]
pub enum InputDirection {
    Up,
    Down,
    Left,
    Right,
    UpLeft,
    UpRight,
    DownLeft,
    DownRight,
    Neutral,
}

impl InputDirection {
    /// Convert to quarter-circle notation
    pub fn to_numpad(self) -> u8 {
        match self {
            InputDirection::DownLeft => 1,
            InputDirection::Down => 2,
            InputDirection::DownRight => 3,
            InputDirection::Left => 4,
            InputDirection::Neutral => 5,
            InputDirection::Right => 6,
            InputDirection::UpLeft => 7,
            InputDirection::Up => 8,
            InputDirection::UpRight => 9,
        }
    }
    
    /// Create from separate horizontal and vertical inputs
    pub fn from_components(horizontal: Option<HorizontalInput>, vertical: Option<VerticalInput>) -> Self {
        match (horizontal, vertical) {
            (None, None) => InputDirection::Neutral,
            (Some(HorizontalInput::Left), None) => InputDirection::Left,
            (Some(HorizontalInput::Right), None) => InputDirection::Right,
            (None, Some(VerticalInput::Up)) => InputDirection::Up,
            (None, Some(VerticalInput::Down)) => InputDirection::Down,
            (Some(HorizontalInput::Left), Some(VerticalInput::Up)) => InputDirection::UpLeft,
            (Some(HorizontalInput::Right), Some(VerticalInput::Up)) => InputDirection::UpRight,
            (Some(HorizontalInput::Left), Some(VerticalInput::Down)) => InputDirection::DownLeft,
            (Some(HorizontalInput::Right), Some(VerticalInput::Down)) => InputDirection::DownRight,
        }
    }
}

/// Horizontal input component
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum HorizontalInput {
    Left,
    Right,
}

/// Vertical input component
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum VerticalInput {
    Up,
    Down,
}

/// Button inputs
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ButtonInput {
    LightPunch,
    MediumPunch,
    HeavyPunch,
    LightKick,
    MediumKick,
    HeavyKick,
}

/// Complete input state at a given frame
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub struct InputState {
    pub direction: InputDirection,
    pub buttons: ButtonFlags,
    pub frame: u64,
}

/// Bit flags for button states
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub struct ButtonFlags(u8);

impl ButtonFlags {
    pub const NONE: Self = Self(0);
    pub const LIGHT_PUNCH: Self = Self(1 << 0);
    pub const MEDIUM_PUNCH: Self = Self(1 << 1);
    pub const HEAVY_PUNCH: Self = Self(1 << 2);
    pub const LIGHT_KICK: Self = Self(1 << 3);
    pub const MEDIUM_KICK: Self = Self(1 << 4);
    pub const HEAVY_KICK: Self = Self(1 << 5);
    
    pub fn new() -> Self {
        Self::NONE
    }
    
    pub fn set_button(&mut self, button: ButtonInput) {
        let flag = match button {
            ButtonInput::LightPunch => Self::LIGHT_PUNCH,
            ButtonInput::MediumPunch => Self::MEDIUM_PUNCH,
            ButtonInput::HeavyPunch => Self::HEAVY_PUNCH,
            ButtonInput::LightKick => Self::LIGHT_KICK,
            ButtonInput::MediumKick => Self::MEDIUM_KICK,
            ButtonInput::HeavyKick => Self::HEAVY_KICK,
        };
        self.0 |= flag.0;
    }
    
    pub fn has_button(&self, button: ButtonInput) -> bool {
        let flag = match button {
            ButtonInput::LightPunch => Self::LIGHT_PUNCH,
            ButtonInput::MediumPunch => Self::MEDIUM_PUNCH,
            ButtonInput::HeavyPunch => Self::HEAVY_PUNCH,
            ButtonInput::LightKick => Self::LIGHT_KICK,
            ButtonInput::MediumKick => Self::MEDIUM_KICK,
            ButtonInput::HeavyKick => Self::HEAVY_KICK,
        };
        (self.0 & flag.0) != 0
    }
    
    pub fn any_punch(&self) -> bool {
        (self.0 & (Self::LIGHT_PUNCH.0 | Self::MEDIUM_PUNCH.0 | Self::HEAVY_PUNCH.0)) != 0
    }
    
    pub fn any_kick(&self) -> bool {
        (self.0 & (Self::LIGHT_KICK.0 | Self::MEDIUM_KICK.0 | Self::HEAVY_KICK.0)) != 0
    }
}

/// Special move input patterns
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum SpecialMovePattern {
    /// Quarter circle forward + punch (236P)
    QuarterCircleForward(ButtonInput),
    /// Quarter circle back + punch (214P)
    QuarterCircleBack(ButtonInput),
    /// Dragon punch motion + punch (623P)
    DragonPunch(ButtonInput),
    /// Charge back, forward + punch
    ChargeBackForward(ButtonInput),
    /// Charge down, up + kick
    ChargeDownUp(ButtonInput),
    /// Half circle back + punch (63214P)
    HalfCircleBack(ButtonInput),
    /// 360 degree rotation + punch
    FullCircle(ButtonInput),
}

impl SpecialMovePattern {
    /// Get the input sequence for this pattern
    pub fn get_sequence(&self) -> Vec<InputDirection> {
        match self {
            SpecialMovePattern::QuarterCircleForward(_) => vec![
                InputDirection::Down,
                InputDirection::DownRight,
                InputDirection::Right,
            ],
            SpecialMovePattern::QuarterCircleBack(_) => vec![
                InputDirection::Down,
                InputDirection::DownLeft,
                InputDirection::Left,
            ],
            SpecialMovePattern::DragonPunch(_) => vec![
                InputDirection::Right,
                InputDirection::Down,
                InputDirection::DownRight,
            ],
            SpecialMovePattern::ChargeBackForward(_) => vec![
                InputDirection::Left, // Hold for ~2 seconds
                InputDirection::Right,
            ],
            SpecialMovePattern::ChargeDownUp(_) => vec![
                InputDirection::Down, // Hold for ~2 seconds
                InputDirection::Up,
            ],
            SpecialMovePattern::HalfCircleBack(_) => vec![
                InputDirection::Right,
                InputDirection::DownRight,
                InputDirection::Down,
                InputDirection::DownLeft,
                InputDirection::Left,
            ],
            SpecialMovePattern::FullCircle(_) => vec![
                InputDirection::Right,
                InputDirection::DownRight,
                InputDirection::Down,
                InputDirection::DownLeft,
                InputDirection::Left,
                InputDirection::UpLeft,
                InputDirection::Up,
                InputDirection::UpRight,
            ],
        }
    }
    
    /// Get the required button for this pattern
    pub fn get_button(&self) -> ButtonInput {
        match self {
            SpecialMovePattern::QuarterCircleForward(btn) |
            SpecialMovePattern::QuarterCircleBack(btn) |
            SpecialMovePattern::DragonPunch(btn) |
            SpecialMovePattern::ChargeBackForward(btn) |
            SpecialMovePattern::ChargeDownUp(btn) |
            SpecialMovePattern::HalfCircleBack(btn) |
            SpecialMovePattern::FullCircle(btn) => *btn,
        }
    }
}

/// C99-compatible input structures matching original engine
/// Equivalent to DUAL structure in C99 code
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
#[repr(C)]
pub struct DualInput {
    pub full: u16,
}

impl DualInput {
    pub const fn new(value: u16) -> Self {
        Self { full: value }
    }

    pub const fn empty() -> Self {
        Self { full: 0 }
    }

    /// Get joystick direction bits (lower 4 bits)
    pub const fn joystick_bits(self) -> u8 {
        (self.full & 0x0F) as u8
    }

    /// Get button bits (upper 12 bits)
    pub const fn button_bits(self) -> u16 {
        self.full & 0xFFF0
    }

    /// Check if specific joystick direction is pressed
    pub const fn has_direction(self, direction: u8) -> bool {
        (self.joystick_bits() & direction) != 0
    }

    /// Check if specific button is pressed
    pub const fn has_button(self, button: u16) -> bool {
        (self.button_bits() & button) != 0
    }
}

/// Input buffer for frame-accurate input tracking
/// Matches C99 input buffer behavior with circular buffer
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FrameInputBuffer {
    /// Circular buffer of input states
    pub buffer: [InputState; 16],
    /// Current write position in buffer
    pub write_index: usize,
    /// Number of valid entries in buffer
    pub count: usize,
    /// Frame counter for timing
    pub current_frame: u64,
}

impl Default for FrameInputBuffer {
    fn default() -> Self {
        Self {
            buffer: [InputState {
                direction: InputDirection::Neutral,
                buttons: ButtonFlags::NONE,
                frame: 0,
            }; 16],
            write_index: 0,
            count: 0,
            current_frame: 0,
        }
    }
}

impl FrameInputBuffer {
    /// Create new input buffer
    pub fn new() -> Self {
        Self::default()
    }

    /// Add new input state to buffer
    pub fn push(&mut self, input: InputState) {
        self.buffer[self.write_index] = input;
        self.write_index = (self.write_index + 1) % self.buffer.len();
        if self.count < self.buffer.len() {
            self.count += 1;
        }
        self.current_frame = input.frame;
    }

    /// Get input state at specific frame offset (0 = current, 1 = previous, etc.)
    pub fn get_at_offset(&self, offset: usize) -> Option<InputState> {
        if offset >= self.count {
            return None;
        }

        let index = if self.write_index >= offset + 1 {
            self.write_index - offset - 1
        } else {
            self.buffer.len() + self.write_index - offset - 1
        };

        Some(self.buffer[index])
    }

    /// Get current input state
    pub fn current(&self) -> Option<InputState> {
        self.get_at_offset(0)
    }

    /// Get previous input state
    pub fn previous(&self) -> Option<InputState> {
        self.get_at_offset(1)
    }

    /// Check if direction was just pressed (not held)
    pub fn direction_just_pressed(&self, direction: InputDirection) -> bool {
        if let (Some(current), Some(previous)) = (self.current(), self.previous()) {
            current.direction == direction && previous.direction != direction
        } else {
            false
        }
    }

    /// Check if button was just pressed (not held)
    pub fn button_just_pressed(&self, button: ButtonInput) -> bool {
        if let (Some(current), Some(previous)) = (self.current(), self.previous()) {
            current.buttons.has_button(button) && !previous.buttons.has_button(button)
        } else {
            false
        }
    }

    /// Get sequence of directions within frame window
    pub fn get_direction_sequence(&self, frames: usize) -> Vec<InputDirection> {
        let mut sequence = Vec::new();
        let max_frames = frames.min(self.count);

        for i in 0..max_frames {
            if let Some(input) = self.get_at_offset(max_frames - 1 - i) {
                sequence.push(input.direction);
            }
        }

        sequence
    }

    /// Clear old inputs beyond specified frame count
    pub fn cleanup_old_inputs(&mut self, max_age_frames: u64) {
        if self.count == 0 {
            return;
        }

        let cutoff_frame = self.current_frame.saturating_sub(max_age_frames);
        let mut new_count = 0;

        for i in 0..self.count {
            if let Some(input) = self.get_at_offset(i) {
                if input.frame >= cutoff_frame {
                    new_count = self.count - i;
                    break;
                }
            }
        }

        self.count = new_count;
    }
}

/// Charge move state tracking
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChargeState {
    /// Current charge direction being held
    pub charge_direction: Option<InputDirection>,
    /// Frames the direction has been held
    pub charge_frames: u32,
    /// Required frames for full charge
    pub required_frames: u32,
    /// Whether charge is complete
    pub is_charged: bool,
}

impl Default for ChargeState {
    fn default() -> Self {
        Self {
            charge_direction: None,
            charge_frames: 0,
            required_frames: crate::constants::input::CHARGE_TIME_REQUIRED,
            is_charged: false,
        }
    }
}

impl ChargeState {
    pub fn new() -> Self {
        Self::default()
    }

    /// Update charge state with current input
    pub fn update(&mut self, current_direction: InputDirection) {
        match self.charge_direction {
            Some(charge_dir) if charge_dir == current_direction => {
                // Continue charging
                self.charge_frames += 1;
                if self.charge_frames >= self.required_frames {
                    self.is_charged = true;
                }
            }
            _ => {
                // Start new charge or reset
                if current_direction == InputDirection::Left ||
                   current_direction == InputDirection::Right ||
                   current_direction == InputDirection::Down ||
                   current_direction == InputDirection::Up {
                    self.charge_direction = Some(current_direction);
                    self.charge_frames = 1;
                    self.is_charged = false;
                } else {
                    self.reset();
                }
            }
        }
    }

    /// Check if charge is complete for specific direction
    pub fn is_charged_for(&self, direction: InputDirection) -> bool {
        self.is_charged && self.charge_direction == Some(direction)
    }

    /// Reset charge state
    pub fn reset(&mut self) {
        self.charge_direction = None;
        self.charge_frames = 0;
        self.is_charged = false;
    }

    /// Consume charge (call when special move is executed)
    pub fn consume(&mut self) {
        self.reset();
    }
}

/// Special move detector for pattern matching
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SpecialMoveDetector {
    /// Input buffer for pattern matching
    pub input_buffer: FrameInputBuffer,
    /// Charge states for back/forward and down/up
    pub charge_back: ChargeState,
    pub charge_forward: ChargeState,
    pub charge_down: ChargeState,
    pub charge_up: ChargeState,
    /// Configuration for timing windows
    pub config: SpecialMoveConfig,
}

/// Configuration for special move detection
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SpecialMoveConfig {
    /// Maximum frames between inputs in a sequence
    pub input_window: u32,
    /// Required charge time for charge moves
    pub charge_time: u32,
    /// Leniency for direction inputs (allows slight imprecision)
    pub direction_leniency: bool,
}

impl Default for SpecialMoveConfig {
    fn default() -> Self {
        Self {
            input_window: crate::constants::input::SPECIAL_MOVE_WINDOW,
            charge_time: crate::constants::input::CHARGE_TIME_REQUIRED,
            direction_leniency: true,
        }
    }
}

impl Default for SpecialMoveDetector {
    fn default() -> Self {
        Self {
            input_buffer: FrameInputBuffer::new(),
            charge_back: ChargeState::new(),
            charge_forward: ChargeState::new(),
            charge_down: ChargeState::new(),
            charge_up: ChargeState::new(),
            config: SpecialMoveConfig::default(),
        }
    }
}

impl SpecialMoveDetector {
    pub fn new() -> Self {
        Self::default()
    }

    /// Update with new input state
    pub fn update(&mut self, input: InputState, facing_direction: FbDirection) {
        self.input_buffer.push(input);

        // Update charge states based on facing direction
        let (back_dir, forward_dir) = match facing_direction {
            FbDirection::FacingLeft => (InputDirection::Right, InputDirection::Left),
            FbDirection::FacingRight => (InputDirection::Left, InputDirection::Right),
        };

        // Update charge states
        if input.direction == back_dir {
            self.charge_back.update(input.direction);
            self.charge_forward.reset();
        } else if input.direction == forward_dir {
            self.charge_forward.update(input.direction);
            self.charge_back.reset();
        } else {
            if input.direction != InputDirection::Down && input.direction != InputDirection::Up {
                self.charge_back.reset();
                self.charge_forward.reset();
            }
        }

        if input.direction == InputDirection::Down {
            self.charge_down.update(input.direction);
            self.charge_up.reset();
        } else if input.direction == InputDirection::Up {
            self.charge_up.update(input.direction);
            self.charge_down.reset();
        } else {
            if input.direction != back_dir && input.direction != forward_dir {
                self.charge_down.reset();
                self.charge_up.reset();
            }
        }
    }

    /// Check for quarter circle forward pattern (236)
    pub fn check_quarter_circle_forward(&self, facing_direction: FbDirection) -> bool {
        let sequence = self.input_buffer.get_direction_sequence(self.config.input_window as usize);
        let (forward_dir, down_forward) = match facing_direction {
            FbDirection::FacingLeft => (InputDirection::Left, InputDirection::DownLeft),
            FbDirection::FacingRight => (InputDirection::Right, InputDirection::DownRight),
        };

        self.match_sequence(&sequence, &[InputDirection::Down, down_forward, forward_dir])
    }

    /// Check for quarter circle back pattern (214)
    pub fn check_quarter_circle_back(&self, facing_direction: FbDirection) -> bool {
        let sequence = self.input_buffer.get_direction_sequence(self.config.input_window as usize);
        let (back_dir, down_back) = match facing_direction {
            FbDirection::FacingLeft => (InputDirection::Right, InputDirection::DownRight),
            FbDirection::FacingRight => (InputDirection::Left, InputDirection::DownLeft),
        };

        self.match_sequence(&sequence, &[InputDirection::Down, down_back, back_dir])
    }

    /// Check for dragon punch pattern (623)
    pub fn check_dragon_punch(&self, facing_direction: FbDirection) -> bool {
        let sequence = self.input_buffer.get_direction_sequence(self.config.input_window as usize);
        let (forward_dir, down_forward) = match facing_direction {
            FbDirection::FacingLeft => (InputDirection::Left, InputDirection::DownLeft),
            FbDirection::FacingRight => (InputDirection::Right, InputDirection::DownRight),
        };

        self.match_sequence(&sequence, &[forward_dir, InputDirection::Down, down_forward])
    }

    /// Check for charge back-forward pattern
    pub fn check_charge_back_forward(&self, facing_direction: FbDirection) -> bool {
        let sequence = self.input_buffer.get_direction_sequence(5); // Short window for release
        let forward_dir = match facing_direction {
            FbDirection::FacingLeft => InputDirection::Left,
            FbDirection::FacingRight => InputDirection::Right,
        };

        self.charge_back.is_charged && sequence.contains(&forward_dir)
    }

    /// Check for charge down-up pattern
    pub fn check_charge_down_up(&self) -> bool {
        let sequence = self.input_buffer.get_direction_sequence(5); // Short window for release
        self.charge_down.is_charged && sequence.contains(&InputDirection::Up)
    }

    /// Check for half circle back pattern (63214)
    pub fn check_half_circle_back(&self, facing_direction: FbDirection) -> bool {
        let sequence = self.input_buffer.get_direction_sequence(self.config.input_window as usize);
        let (forward_dir, down_forward, down_back, back_dir) = match facing_direction {
            FbDirection::FacingLeft => (
                InputDirection::Left,
                InputDirection::DownLeft,
                InputDirection::DownRight,
                InputDirection::Right,
            ),
            FbDirection::FacingRight => (
                InputDirection::Right,
                InputDirection::DownRight,
                InputDirection::DownLeft,
                InputDirection::Left,
            ),
        };

        self.match_sequence(&sequence, &[
            forward_dir,
            down_forward,
            InputDirection::Down,
            down_back,
            back_dir,
        ])
    }

    /// Helper method to match input sequence with pattern
    fn match_sequence(&self, sequence: &[InputDirection], pattern: &[InputDirection]) -> bool {
        if sequence.len() < pattern.len() {
            return false;
        }

        // Look for pattern at the end of the sequence (most recent inputs)
        let start_pos = sequence.len() - pattern.len();
        for i in 0..pattern.len() {
            if !self.directions_match(sequence[start_pos + i], pattern[i]) {
                return false;
            }
        }

        true
    }

    /// Check if two directions match with optional leniency
    fn directions_match(&self, input: InputDirection, pattern: InputDirection) -> bool {
        if input == pattern {
            return true;
        }

        if !self.config.direction_leniency {
            return false;
        }

        // Allow some leniency for diagonal inputs
        match (input, pattern) {
            (InputDirection::Down, InputDirection::DownLeft) |
            (InputDirection::Down, InputDirection::DownRight) |
            (InputDirection::DownLeft, InputDirection::Down) |
            (InputDirection::DownRight, InputDirection::Down) => true,
            _ => false,
        }
    }

    /// Detect any special move pattern with button press
    pub fn detect_special_move(&mut self, facing_direction: FbDirection) -> Option<SpecialMovePattern> {
        // Check if any button was just pressed
        if let Some(_current) = self.input_buffer.current() {
            for button in [
                ButtonInput::LightPunch,
                ButtonInput::MediumPunch,
                ButtonInput::HeavyPunch,
                ButtonInput::LightKick,
                ButtonInput::MediumKick,
                ButtonInput::HeavyKick,
            ] {
                if self.input_buffer.button_just_pressed(button) {
                    // Check patterns in order of complexity
                    if self.check_charge_back_forward(facing_direction) {
                        self.charge_back.consume();
                        return Some(SpecialMovePattern::ChargeBackForward(button));
                    }

                    if self.check_charge_down_up() {
                        self.charge_down.consume();
                        return Some(SpecialMovePattern::ChargeDownUp(button));
                    }

                    if self.check_half_circle_back(facing_direction) {
                        return Some(SpecialMovePattern::HalfCircleBack(button));
                    }

                    if self.check_dragon_punch(facing_direction) {
                        return Some(SpecialMovePattern::DragonPunch(button));
                    }

                    if self.check_quarter_circle_forward(facing_direction) {
                        return Some(SpecialMovePattern::QuarterCircleForward(button));
                    }

                    if self.check_quarter_circle_back(facing_direction) {
                        return Some(SpecialMovePattern::QuarterCircleBack(button));
                    }
                }
            }
        }

        None
    }
}

/// C99 compatibility layer for input handling
/// Matches the original engine's input processing structures

/// JoyDecode structure (equivalent to C99 JoyDecode)
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
#[repr(C)]
pub struct JoyDecode {
    pub full: u16,
}

impl JoyDecode {
    pub const fn new(value: u16) -> Self {
        Self { full: value }
    }

    pub const fn empty() -> Self {
        Self { full: 0 }
    }

    /// Get joystick bits (matching C99 JOY_* constants)
    pub const fn joy_up(self) -> bool {
        (self.full & 0x8) != 0
    }

    pub const fn joy_down(self) -> bool {
        (self.full & 0x4) != 0
    }

    pub const fn joy_left(self) -> bool {
        (self.full & 0x2) != 0
    }

    pub const fn joy_right(self) -> bool {
        (self.full & 0x1) != 0
    }

    /// Get button bits (matching C99 BUTTON_* constants)
    pub const fn button_a(self) -> bool {
        (self.full & 0x10) != 0
    }

    pub const fn button_b(self) -> bool {
        (self.full & 0x20) != 0
    }

    pub const fn button_c(self) -> bool {
        (self.full & 0x40) != 0
    }

    pub const fn button_d(self) -> bool {
        (self.full & 0x100) != 0
    }

    pub const fn button_e(self) -> bool {
        (self.full & 0x200) != 0
    }

    pub const fn button_f(self) -> bool {
        (self.full & 0x400) != 0
    }

    /// Get movement mask (all directional inputs)
    pub const fn movement_mask(self) -> u16 {
        self.full & 0xF
    }

    /// Get button mask (all button inputs)
    pub const fn button_mask(self) -> u16 {
        self.full & 0x7F0
    }
}

/// JoyCorrect structure for processed input (equivalent to C99 JoyCorrect)
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
#[repr(C)]
pub struct JoyCorrect {
    pub joy_correct: u8,      // Processed joystick input
    pub joy_correct2: u8,     // Secondary processed input
    pub joy_correct_dash: u8, // Dash input processing
    pub joy_correct_dash2: u8, // Secondary dash processing
}

impl Default for JoyCorrect {
    fn default() -> Self {
        Self {
            joy_correct: 0,
            joy_correct2: 0,
            joy_correct_dash: 0,
            joy_correct_dash2: 0,
        }
    }
}

impl JoyCorrect {
    pub fn new() -> Self {
        Self::default()
    }

    /// Process raw input through C99-compatible algorithm
    pub fn process_input(joy_decode: JoyDecode, joy_decode_dash: JoyDecode, direction: FbDirection) -> Self {
        let mut a = joy_decode.full;
        let mut b = joy_decode_dash.full;
        let c = a;
        let _d = b;

        // Apply direction flip logic (matching C99 LBDecodeInputs)
        if direction == FbDirection::FacingLeft {
            a &= 0x77c;
            b &= 0x77c;
            if (c & 2) != 0 {
                a |= 1;
                b |= 1;
            }
            if (c & 1) != 0 {
                a |= 2;
                b |= 2;
            }
        }

        Self {
            joy_correct: ((a & 0xff00) >> 8) as u8,
            joy_correct2: (a & 0xff) as u8,
            joy_correct_dash: ((b & 0xff00) >> 8) as u8,
            joy_correct_dash2: (b & 0xff) as u8,
        }
    }
}

/// Complete C99-compatible input state
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
#[repr(C)]
pub struct C99InputState {
    pub joy_decode: JoyDecode,
    pub joy_decode_dash: JoyDecode,
    pub joy_correct: JoyCorrect,
    pub raw_buttons0: u8,
    pub raw_buttons0_dash: u8,
    pub raw_buttons0_dash2: u8,
    pub raw_buttons0_dash3: u8,
}

impl Default for C99InputState {
    fn default() -> Self {
        Self {
            joy_decode: JoyDecode::empty(),
            joy_decode_dash: JoyDecode::empty(),
            joy_correct: JoyCorrect::new(),
            raw_buttons0: 0,
            raw_buttons0_dash: 0,
            raw_buttons0_dash2: 0,
            raw_buttons0_dash3: 0,
        }
    }
}

impl C99InputState {
    pub fn new() -> Self {
        Self::default()
    }

    /// Update input state with new raw input
    pub fn update(&mut self, new_input: JoyDecode, direction: FbDirection) {
        // Store previous input as dash
        self.joy_decode_dash = self.joy_decode;
        self.joy_decode = new_input;

        // Process through C99 algorithm
        self.joy_correct = JoyCorrect::process_input(self.joy_decode, self.joy_decode_dash, direction);

        // Update raw button states
        self.raw_buttons0_dash3 = self.raw_buttons0_dash2;
        self.raw_buttons0_dash2 = self.raw_buttons0_dash;
        self.raw_buttons0_dash = self.raw_buttons0;
        self.raw_buttons0 = (new_input.full >> 8) as u8;
    }

    /// Convert modern InputState to C99 format
    pub fn from_modern_input(input: InputState, direction: FbDirection) -> Self {
        let mut joy_value = 0u16;

        // Convert direction to C99 format
        match input.direction {
            InputDirection::Up => joy_value |= 0x8,
            InputDirection::Down => joy_value |= 0x4,
            InputDirection::Left => joy_value |= 0x2,
            InputDirection::Right => joy_value |= 0x1,
            InputDirection::UpLeft => joy_value |= 0x8 | 0x2,
            InputDirection::UpRight => joy_value |= 0x8 | 0x1,
            InputDirection::DownLeft => joy_value |= 0x4 | 0x2,
            InputDirection::DownRight => joy_value |= 0x4 | 0x1,
            InputDirection::Neutral => {},
        }

        // Convert buttons to C99 format
        if input.buttons.has_button(ButtonInput::LightPunch) {
            joy_value |= 0x10;
        }
        if input.buttons.has_button(ButtonInput::MediumPunch) {
            joy_value |= 0x20;
        }
        if input.buttons.has_button(ButtonInput::HeavyPunch) {
            joy_value |= 0x40;
        }
        if input.buttons.has_button(ButtonInput::LightKick) {
            joy_value |= 0x100;
        }
        if input.buttons.has_button(ButtonInput::MediumKick) {
            joy_value |= 0x200;
        }
        if input.buttons.has_button(ButtonInput::HeavyKick) {
            joy_value |= 0x400;
        }

        let mut state = Self::new();
        state.update(JoyDecode::new(joy_value), direction);
        state
    }
}
