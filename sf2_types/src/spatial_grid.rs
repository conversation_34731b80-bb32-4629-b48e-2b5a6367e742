//! # Spatial Partitioning Grid
//!
//! Efficient spatial partitioning system for broad-phase collision detection.
//! Divides the game world into a grid to quickly eliminate distant collision pairs.

use crate::geometry::*;
use crate::collision_config::CollisionConfig;
use bevy::prelude::*;
use std::collections::HashMap;

/// Entity ID for spatial grid tracking
pub type SpatialEntityId = Entity;

/// Spatial grid cell coordinates
#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq, Hash)]
pub struct GridCell {
    pub x: i32,
    pub y: i32,
}

impl GridCell {
    pub fn new(x: i32, y: i32) -> Self {
        Self { x, y }
    }
    
    /// Get neighboring cells (including self)
    pub fn neighbors(&self) -> [GridCell; 9] {
        [
            GridCell::new(self.x - 1, self.y - 1),
            GridCell::new(self.x,     self.y - 1),
            GridCell::new(self.x + 1, self.y - 1),
            GridCell::new(self.x - 1, self.y),
            GridCell::new(self.x,     self.y),
            GridCell::new(self.x + 1, self.y),
            GridCell::new(self.x - 1, self.y + 1),
            GridCell::new(self.x,     self.y + 1),
            GridCell::new(self.x + 1, self.y + 1),
        ]
    }
}

/// Spatial grid entry containing entity and its bounds
#[derive(Debug, Clone)]
pub struct SpatialEntry {
    pub entity: SpatialEntityId,
    pub bounds: Rect8,
    pub layer: CollisionLayer,
}

/// Collision layers for filtering
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
pub enum CollisionLayer {
    Fighter,
    Projectile,
    Environment,
    Effect,
}

/// Spatial partitioning grid for efficient collision detection
#[derive(Debug, Resource)]
pub struct SpatialGrid {
    /// Grid cell size in pixels
    cell_size: u16,
    /// Grid cells containing entities
    cells: HashMap<GridCell, Vec<SpatialEntry>>,
    /// Entity to cell mapping for quick updates
    entity_cells: HashMap<SpatialEntityId, Vec<GridCell>>,
    /// World bounds for the grid
    world_bounds: IntRect,
    /// Performance metrics
    metrics: SpatialGridMetrics,
}

/// Performance metrics for spatial grid
#[derive(Debug, Default, Clone, Copy)]
pub struct SpatialGridMetrics {
    /// Number of entities in grid
    pub entity_count: usize,
    /// Number of active cells
    pub active_cells: usize,
    /// Average entities per cell
    pub avg_entities_per_cell: f32,
    /// Maximum entities in a single cell
    pub max_entities_per_cell: usize,
    /// Number of collision pairs generated
    pub collision_pairs_generated: usize,
    /// Broad-phase elimination rate
    pub elimination_rate: f32,
}

impl Default for SpatialGrid {
    fn default() -> Self {
        let default_config = CollisionConfig::default();
        let default_bounds = IntRect::new(-400, -300, 800, 600);
        Self::new(&default_config, default_bounds)
    }
}

impl SpatialGrid {
    /// Create a new spatial grid
    pub fn new(config: &CollisionConfig, world_bounds: IntRect) -> Self {
        Self {
            cell_size: config.spatial_grid_size,
            cells: HashMap::new(),
            entity_cells: HashMap::new(),
            world_bounds,
            metrics: SpatialGridMetrics::default(),
        }
    }
    
    /// Clear all entities from the grid
    pub fn clear(&mut self) {
        self.cells.clear();
        self.entity_cells.clear();
        self.metrics = SpatialGridMetrics::default();
    }
    
    /// Convert world coordinates to grid cell
    pub fn world_to_cell(&self, x: i32, y: i32) -> GridCell {
        GridCell::new(
            x / self.cell_size as i32,
            y / self.cell_size as i32,
        )
    }
    
    /// Convert rectangle to grid cells it overlaps
    pub fn rect_to_cells(&self, rect: &Rect8) -> Vec<GridCell> {
        let left = rect.left() as i32;
        let right = rect.right() as i32;
        let top = rect.top() as i32;
        let bottom = rect.bottom() as i32;
        
        let min_cell = self.world_to_cell(left, top);
        let max_cell = self.world_to_cell(right, bottom);
        
        let mut cells = Vec::new();
        for y in min_cell.y..=max_cell.y {
            for x in min_cell.x..=max_cell.x {
                cells.push(GridCell::new(x, y));
            }
        }
        cells
    }
    
    /// Insert an entity into the grid
    pub fn insert(&mut self, entity: SpatialEntityId, bounds: Rect8, layer: CollisionLayer) {
        // Remove entity if it already exists
        self.remove(entity);
        
        let cells = self.rect_to_cells(&bounds);
        let entry = SpatialEntry {
            entity,
            bounds,
            layer,
        };
        
        // Insert into each overlapping cell
        for cell in &cells {
            self.cells.entry(*cell).or_insert_with(Vec::new).push(entry.clone());
        }
        
        // Track which cells this entity occupies
        self.entity_cells.insert(entity, cells);
        
        // Update metrics
        self.update_metrics();
    }
    
    /// Remove an entity from the grid
    pub fn remove(&mut self, entity: SpatialEntityId) {
        if let Some(cells) = self.entity_cells.remove(&entity) {
            for cell in cells {
                if let Some(entries) = self.cells.get_mut(&cell) {
                    entries.retain(|entry| entry.entity != entity);
                    
                    // Remove empty cells
                    if entries.is_empty() {
                        self.cells.remove(&cell);
                    }
                }
            }
        }
        
        // Update metrics
        self.update_metrics();
    }
    
    /// Update an entity's position in the grid
    pub fn update(&mut self, entity: SpatialEntityId, new_bounds: Rect8, layer: CollisionLayer) {
        // Check if entity needs to move cells
        let new_cells = self.rect_to_cells(&new_bounds);
        
        if let Some(old_cells) = self.entity_cells.get(&entity) {
            // If cells haven't changed, just update bounds
            if old_cells.len() == new_cells.len() && old_cells.iter().all(|c| new_cells.contains(c)) {
                // Update bounds in existing cells
                for cell in old_cells {
                    if let Some(entries) = self.cells.get_mut(cell) {
                        for entry in entries {
                            if entry.entity == entity {
                                entry.bounds = new_bounds;
                                break;
                            }
                        }
                    }
                }
                return;
            }
        }
        
        // Entity moved to different cells, remove and re-insert
        self.remove(entity);
        self.insert(entity, new_bounds, layer);
    }
    
    /// Query entities in a region
    pub fn query_region(&self, bounds: &Rect8, layer_filter: Option<CollisionLayer>) -> Vec<SpatialEntry> {
        let cells = self.rect_to_cells(bounds);
        let mut results = Vec::new();
        let mut seen_entities = std::collections::HashSet::new();
        
        for cell in cells {
            if let Some(entries) = self.cells.get(&cell) {
                for entry in entries {
                    // Avoid duplicates (entity can be in multiple cells)
                    if seen_entities.contains(&entry.entity) {
                        continue;
                    }
                    
                    // Apply layer filter
                    if let Some(filter_layer) = layer_filter {
                        if entry.layer != filter_layer {
                            continue;
                        }
                    }
                    
                    // Check if bounds actually overlap
                    if bounds.intersects(&entry.bounds) {
                        results.push(entry.clone());
                        seen_entities.insert(entry.entity);
                    }
                }
            }
        }
        
        results
    }
    
    /// Get potential collision pairs for an entity
    pub fn get_collision_candidates(&self, entity: SpatialEntityId) -> Vec<SpatialEntry> {
        if let Some(cells) = self.entity_cells.get(&entity) {
            let mut candidates = Vec::new();
            let mut seen_entities = std::collections::HashSet::new();
            
            for cell in cells {
                if let Some(entries) = self.cells.get(cell) {
                    for entry in entries {
                        // Skip self
                        if entry.entity == entity {
                            continue;
                        }
                        
                        // Avoid duplicates
                        if seen_entities.contains(&entry.entity) {
                            continue;
                        }
                        
                        candidates.push(entry.clone());
                        seen_entities.insert(entry.entity);
                    }
                }
            }
            
            candidates
        } else {
            Vec::new()
        }
    }
    
    /// Generate all potential collision pairs
    pub fn generate_collision_pairs(&mut self) -> Vec<(SpatialEntry, SpatialEntry)> {
        let mut pairs = Vec::new();
        let mut processed_pairs = std::collections::HashSet::new();
        
        for entries in self.cells.values() {
            // Generate pairs within each cell
            for i in 0..entries.len() {
                for j in (i + 1)..entries.len() {
                    let entity1 = entries[i].entity;
                    let entity2 = entries[j].entity;
                    
                    // Create a consistent pair key
                    let pair_key = if entity1 < entity2 {
                        (entity1, entity2)
                    } else {
                        (entity2, entity1)
                    };
                    
                    // Skip if we've already processed this pair
                    if processed_pairs.contains(&pair_key) {
                        continue;
                    }
                    
                    processed_pairs.insert(pair_key);
                    pairs.push((entries[i].clone(), entries[j].clone()));
                }
            }
        }
        
        self.metrics.collision_pairs_generated = pairs.len();
        pairs
    }
    
    /// Update performance metrics
    fn update_metrics(&mut self) {
        self.metrics.entity_count = self.entity_cells.len();
        self.metrics.active_cells = self.cells.len();
        
        if self.metrics.active_cells > 0 {
            let total_entries: usize = self.cells.values().map(|v| v.len()).sum();
            self.metrics.avg_entities_per_cell = total_entries as f32 / self.metrics.active_cells as f32;
            self.metrics.max_entities_per_cell = self.cells.values().map(|v| v.len()).max().unwrap_or(0);
        } else {
            self.metrics.avg_entities_per_cell = 0.0;
            self.metrics.max_entities_per_cell = 0;
        }
    }
    
    /// Get performance metrics
    pub fn metrics(&self) -> &SpatialGridMetrics {
        &self.metrics
    }
    
    /// Resize the grid (useful for dynamic world sizes)
    pub fn resize(&mut self, new_cell_size: u16, new_world_bounds: IntRect) {
        // Store all entities
        let entities: Vec<_> = self.entity_cells.keys().copied().collect();
        let mut entity_data = Vec::new();
        
        for entity in entities {
            if let Some(cells) = self.entity_cells.get(&entity) {
                if let Some(cell) = cells.first() {
                    if let Some(entries) = self.cells.get(cell) {
                        if let Some(entry) = entries.iter().find(|e| e.entity == entity) {
                            entity_data.push((entity, entry.bounds, entry.layer));
                        }
                    }
                }
            }
        }
        
        // Clear and reconfigure
        self.clear();
        self.cell_size = new_cell_size;
        self.world_bounds = new_world_bounds;
        
        // Re-insert all entities
        for (entity, bounds, layer) in entity_data {
            self.insert(entity, bounds, layer);
        }
    }
}
