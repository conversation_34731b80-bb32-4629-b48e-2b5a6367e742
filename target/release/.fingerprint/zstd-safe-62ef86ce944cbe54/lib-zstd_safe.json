{"rustc": 15497389221046826682, "features": "[\"std\"]", "declared_features": "[\"arrays\", \"bindgen\", \"debug\", \"default\", \"doc-cfg\", \"experimental\", \"fat-lto\", \"legacy\", \"no_asm\", \"pkg-config\", \"seekable\", \"std\", \"thin\", \"thin-lto\", \"zdict_builder\", \"zstdmt\"]", "target": 13834647262792939399, "profile": 10802215680348010833, "path": 13535854932407606048, "deps": [[8373447648276846408, "zstd_sys", false, 3689008214545433221], [15788444815745660356, "build_script_build", false, 7473567463793387053]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/zstd-safe-62ef86ce944cbe54/dep-lib-zstd_safe", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}