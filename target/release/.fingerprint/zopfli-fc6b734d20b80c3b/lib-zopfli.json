{"rustc": 15497389221046826682, "features": "[\"default\", \"gzip\", \"std\", \"zlib\"]", "declared_features": "[\"default\", \"gzip\", \"nightly\", \"std\", \"zlib\"]", "target": 10788940031695432753, "profile": 16503403049695105087, "path": 6473682949730790528, "deps": [[4018467389006652250, "simd_adler32", false, 12406173764372672527], [5466618496199522463, "crc32fast", false, 870850457070576110], [5986029879202738730, "log", false, 8848497622669772457], [13336078982182647123, "<PERSON><PERSON>", false, 6645609055824403588]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/zopfli-fc6b734d20b80c3b/dep-lib-zopfli", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}