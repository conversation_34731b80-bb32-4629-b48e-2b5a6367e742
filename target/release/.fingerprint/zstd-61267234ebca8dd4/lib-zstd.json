{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"arrays\", \"bindgen\", \"debug\", \"default\", \"doc-cfg\", \"experimental\", \"fat-lto\", \"legacy\", \"no_asm\", \"pkg-config\", \"thin\", \"thin-lto\", \"wasm\", \"zdict_builder\", \"zstdmt\"]", "target": 13967053409313941148, "profile": 16503403049695105087, "path": 7601676039284366050, "deps": [[15788444815745660356, "zstd_safe", false, 8614932131301640135]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/zstd-61267234ebca8dd4/dep-lib-zstd", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}