{"rustc": 15497389221046826682, "features": "[\"android-game-activity\", \"android_shared_stdcxx\", \"animation\", \"bevy_animation\", \"bevy_asset\", \"bevy_audio\", \"bevy_color\", \"bevy_core_pipeline\", \"bevy_gilrs\", \"bevy_gizmos\", \"bevy_gltf\", \"bevy_mesh_picking_backend\", \"bevy_pbr\", \"bevy_picking\", \"bevy_render\", \"bevy_scene\", \"bevy_sprite\", \"bevy_sprite_picking_backend\", \"bevy_state\", \"bevy_text\", \"bevy_ui\", \"bevy_ui_picking_backend\", \"bevy_window\", \"bevy_winit\", \"custom_cursor\", \"default\", \"default_font\", \"hdr\", \"ktx2\", \"multi_threaded\", \"png\", \"smaa_luts\", \"sysinfo_plugin\", \"tonemapping_luts\", \"vorbis\", \"wayland\", \"webgl2\", \"x11\", \"zstd\"]", "declared_features": "[\"accesskit_unix\", \"android-game-activity\", \"android-native-activity\", \"android_shared_stdcxx\", \"animation\", \"asset_processor\", \"async-io\", \"basis-universal\", \"bevy_animation\", \"bevy_asset\", \"bevy_audio\", \"bevy_ci_testing\", \"bevy_color\", \"bevy_core_pipeline\", \"bevy_debug_stepping\", \"bevy_dev_tools\", \"bevy_gilrs\", \"bevy_gizmos\", \"bevy_gltf\", \"bevy_image\", \"bevy_mesh_picking_backend\", \"bevy_pbr\", \"bevy_picking\", \"bevy_remote\", \"bevy_render\", \"bevy_scene\", \"bevy_sprite\", \"bevy_sprite_picking_backend\", \"bevy_state\", \"bevy_text\", \"bevy_ui\", \"bevy_ui_picking_backend\", \"bevy_window\", \"bevy_winit\", \"bmp\", \"custom_cursor\", \"dds\", \"debug_glam_assert\", \"default\", \"default_font\", \"detailed_trace\", \"dynamic_linking\", \"embedded_watcher\", \"experimental_pbr_pcss\", \"exr\", \"ff\", \"file_watcher\", \"flac\", \"ghost_nodes\", \"gif\", \"glam_assert\", \"hdr\", \"ico\", \"ios_simulator\", \"jpeg\", \"ktx2\", \"meshlet\", \"meshlet_processor\", \"minimp3\", \"mp3\", \"multi_threaded\", \"pbr_anisotropy_texture\", \"pbr_multi_layer_material_textures\", \"pbr_transmission_textures\", \"png\", \"pnm\", \"qoi\", \"reflect_functions\", \"serialize\", \"shader_format_glsl\", \"shader_format_spirv\", \"smaa_luts\", \"spirv_shader_passthrough\", \"symphonia-aac\", \"symphonia-all\", \"symphonia-flac\", \"symphonia-isomp4\", \"symphonia-vorbis\", \"symphonia-wav\", \"sysinfo_plugin\", \"tga\", \"tiff\", \"tonemapping_luts\", \"trace\", \"trace_chrome\", \"trace_tracy\", \"trace_tracy_memory\", \"track_change_detection\", \"vorbis\", \"wav\", \"wayland\", \"webgl2\", \"webgpu\", \"webp\", \"x11\", \"zlib\", \"zstd\"]", "target": 9072619617608988187, "profile": 18361463550381806219, "path": 5880171780938216591, "deps": [[5513620104112589859, "bevy_internal", false, 9799080852324955269]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/bevy-4fe7930b30a9dee1/dep-lib-bevy", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}