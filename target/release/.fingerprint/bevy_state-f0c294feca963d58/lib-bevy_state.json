{"rustc": 15497389221046826682, "features": "[\"bevy_app\", \"bevy_hierarchy\", \"bevy_reflect\", \"default\"]", "declared_features": "[\"bevy_app\", \"bevy_hierarchy\", \"bevy_reflect\", \"default\"]", "target": 4104521996849610945, "profile": 575762615675316395, "path": 2160350834066085205, "deps": [[5075818945153588980, "bevy_hierarchy", false, 15835517099817124061], [6214442870082674230, "bevy_ecs", false, 630762579400742952], [8922671035508983237, "bevy_state_macros", false, 15295978780308430107], [13543630808651616527, "bevy_utils", false, 7673186190672971801], [13932235871545406073, "bevy_app", false, 15121706988166773443], [15953891987145646537, "bevy_reflect", false, 17759736896543012823]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/bevy_state-f0c294feca963d58/dep-lib-bevy_state", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}