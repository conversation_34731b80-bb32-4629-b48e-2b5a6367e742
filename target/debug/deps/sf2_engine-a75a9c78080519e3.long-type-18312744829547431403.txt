(for<'a, 'b, 'c, 'd, 'e, 'f> fn(bevy::prelude::ResMut<'a, SF2SpriteManager>, bevy::prelude::Res<'b, sf2_assets::ExtractedSprites>, bevy::prelude::Res<'c, SpriteExtractorResource>, bevy::prelude::ResMut<'d, bevy::prelude::Assets<bevy::prelude::Image>>, bevy::prelude::Commands<'e, 'f>) {load_character_sprites_system}, for<'a, 'b, 'c, 'd, 'e, 'f> fn(bevy::prelude::Query<'a, 'b, (bevy::prelude::Entity, &'c components::Fighter, &'d mut sprite_graphics::SF2CharacterSprite), bevy::prelude::Added<components::Fighter>>, bevy::prelude::Commands<'e, 'f>) {update_character_sprite_system}, for<'a, 'b, 'c, 'd, 'e, 'f, 'g, 'h, 'i> fn(bevy::prelude::Query<'a, 'b, (&'c mut sprite_graphics::SF2CharacterSprite, &'d mut components::AnimationState, &'e mut bevy::prelude::Sprite, &'f mut bevy::prelude::Handle<bevy::prelude::Image>)>, bevy::prelude::Res<'g, SF2SpriteManager>, bevy::prelude::Res<'h, SF2AnimationDefinitions>, bevy::prelude::Res<'i, bevy::prelude::Time>) {animate_character_sprites_system})
