{"rustc": 15497389221046826682, "features": "[\"alloc\", \"bevy\", \"debug\", \"debug_stack\", \"default\", \"glam\", \"petgraph\", \"smallvec\", \"smol_str\", \"uuid\"]", "declared_features": "[\"alloc\", \"bevy\", \"debug\", \"debug_stack\", \"default\", \"documentation\", \"functions\", \"glam\", \"petgraph\", \"smallvec\", \"smol_str\", \"uuid\", \"wgpu-types\"]", "target": 16754485857268995592, "profile": 1058352400131300129, "path": 16419020219377713684, "deps": [[2425088982514975140, "bevy_ptr", false, 17020633480272987158], [2831851536307977279, "glam", false, 13533547507840710799], [3571374251074753029, "smol_str", false, 11837335302531907217], [3666196340704888985, "smallvec", false, 9504253755919155840], [8942107105684987126, "erased_serde", false, 12445641005418765480], [9689903380558560274, "serde", false, 3061191315961435077], [11434239582363224126, "downcast_rs", false, 16571129334598469010], [13487854193495724092, "derive_more", false, 4695521678521511335], [13543630808651616527, "bevy_utils", false, 14857909225077134228], [14966697905761911795, "assert_type_match", false, 7640053958337117], [15918101059100394961, "disqualified", false, 5436175543961816237], [15949116387755617522, "uuid", false, 13927242926481709003], [16532555906320553198, "petgraph", false, 5303883896225314858], [17052824281933501444, "bevy_reflect_derive", false, 10982441253475376349]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/bevy_reflect-4f7b1019fbc25a6b/dep-lib-bevy_reflect", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}