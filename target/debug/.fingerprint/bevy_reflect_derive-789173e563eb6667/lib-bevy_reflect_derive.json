{"rustc": 15497389221046826682, "features": "[\"default\"]", "declared_features": "[\"default\", \"documentation\", \"functions\"]", "target": 1736263705675780695, "profile": 10659492051931546360, "path": 5443612649198867505, "deps": [[2196764441672005416, "bevy_macro_utils", false, 5746395216985979148], [3060637413840920116, "proc_macro2", false, 10081079551674933502], [4974441333307933176, "syn", false, 627267355364824689], [15949116387755617522, "uuid", false, 4007259434524081121], [17990358020177143287, "quote", false, 13059664243777791692]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/bevy_reflect_derive-789173e563eb6667/dep-lib-bevy_reflect_derive", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}