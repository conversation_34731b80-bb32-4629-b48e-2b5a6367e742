{"$message_type":"diagnostic","message":"unused variable: `current`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_types/src/input.rs","byte_start":22346,"byte_end":22353,"line_start":661,"line_end":661,"column_start":21,"column_end":28,"is_primary":true,"text":[{"text":"        if let Some(current) = self.input_buffer.current() {","highlight_start":21,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_types/src/input.rs","byte_start":22346,"byte_end":22353,"line_start":661,"line_end":661,"column_start":21,"column_end":28,"is_primary":true,"text":[{"text":"        if let Some(current) = self.input_buffer.current() {","highlight_start":21,"highlight_end":28}],"label":null,"suggested_replacement":"_current","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `current`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_types/src/input.rs:661:21\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m661\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        if let Some(current) = self.input_buffer.current() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_current`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `d`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_types/src/input.rs","byte_start":26630,"byte_end":26631,"line_start":808,"line_end":808,"column_start":13,"column_end":14,"is_primary":true,"text":[{"text":"        let d = b;","highlight_start":13,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_types/src/input.rs","byte_start":26630,"byte_end":26631,"line_start":808,"line_end":808,"column_start":13,"column_end":14,"is_primary":true,"text":[{"text":"        let d = b;","highlight_start":13,"highlight_end":14}],"label":null,"suggested_replacement":"_d","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `d`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_types/src/input.rs:808:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m808\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let d = b;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_d`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `damage`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_types/src/fighter_state.rs","byte_start":16031,"byte_end":16037,"line_start":492,"line_end":492,"column_start":33,"column_end":39,"is_primary":true,"text":[{"text":"    pub fn apply_hit(&mut self, damage: u16, stun: u16, knockback: bool) -> FighterStateTransition {","highlight_start":33,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_types/src/fighter_state.rs","byte_start":16031,"byte_end":16037,"line_start":492,"line_end":492,"column_start":33,"column_end":39,"is_primary":true,"text":[{"text":"    pub fn apply_hit(&mut self, damage: u16, stun: u16, knockback: bool) -> FighterStateTransition {","highlight_start":33,"highlight_end":39}],"label":null,"suggested_replacement":"_damage","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `damage`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_types/src/fighter_state.rs:492:33\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m492\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn apply_hit(&mut self, damage: u16, stun: u16, knockback: bool) -> FighterStateTransition {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_damage`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `precision`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_types/src/collision_detection.rs","byte_start":5385,"byte_end":5394,"line_start":162,"line_end":162,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"        precision: CollisionPrecision,","highlight_start":9,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_types/src/collision_detection.rs","byte_start":5385,"byte_end":5394,"line_start":162,"line_end":162,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"        precision: CollisionPrecision,","highlight_start":9,"highlight_end":18}],"label":null,"suggested_replacement":"_precision","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `precision`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_types/src/collision_detection.rs:162:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m162\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        precision: CollisionPrecision,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_precision`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"4 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 4 warnings emitted\u001b[0m\n\n"}
