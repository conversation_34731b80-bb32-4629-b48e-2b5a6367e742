{"rustc": 15497389221046826682, "features": "[\"std\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-derive-ui-tests\", \"unstable-doc\", \"unstable-ext\", \"unstable-markdown\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 4238846637535193678, "profile": 11234856576084615152, "path": 10421460378823036346, "deps": [[14814905555676593471, "clap_builder", false, 7217964924386926542]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/clap-0b0a94d947e809fe/dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}