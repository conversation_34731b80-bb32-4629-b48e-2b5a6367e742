{"rustc": 15497389221046826682, "features": "[\"alloc\", \"default\", \"fs\", \"std\"]", "declared_features": "[\"all-apis\", \"alloc\", \"compiler_builtins\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"libc\", \"libc_errno\", \"linux_4_11\", \"linux_5_1\", \"linux_5_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"param\", \"pipe\", \"process\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 16221545317719767766, "profile": 15001471041953442873, "path": 8823220324589195036, "deps": [[4684437522915235464, "libc", false, 4722339281664419403], [7896293946984509699, "bitflags", false, 13770117782452544914], [8253628577145923712, "libc_errno", false, 5617845866733893919], [12053020504183902936, "build_script_build", false, 15967351601007384296]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustix-f5028cac5de83e1e/dep-lib-rustix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}