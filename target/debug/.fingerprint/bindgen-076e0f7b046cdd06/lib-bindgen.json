{"rustc": 15497389221046826682, "features": "[\"runtime\"]", "declared_features": "[\"__cli\", \"__testing_only_extra_assertions\", \"__testing_only_libclang_16\", \"__testing_only_libclang_9\", \"default\", \"experimental\", \"logging\", \"prettyplease\", \"runtime\", \"static\"]", "target": 3886691532138051063, "profile": 15736761312856127135, "path": 16016369302806891966, "deps": [[950716570147248582, "cexpr", false, 15878688505582887253], [1711752468582068132, "build_script_build", false, 4185439608864914580], [3060637413840920116, "proc_macro2", false, 10081079551674933502], [3317542222502007281, "itertools", false, 4948423382208947703], [4885725550624711673, "clang_sys", false, 6426054243647336848], [4974441333307933176, "syn", false, 627267355364824689], [7896293946984509699, "bitflags", false, 1578913899571833519], [8410525223747752176, "shlex", false, 8863411223898646589], [9451456094439810778, "regex", false, 14044831703854577776], [17990358020177143287, "quote", false, 13059664243777791692], [18335655851112826545, "rustc_hash", false, 823850049666916561]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/bindgen-076e0f7b046cdd06/dep-lib-bindgen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}