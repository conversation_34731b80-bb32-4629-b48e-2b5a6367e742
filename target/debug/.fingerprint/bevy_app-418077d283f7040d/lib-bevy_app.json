{"rustc": 15497389221046826682, "features": "[\"bevy_reflect\", \"default\"]", "declared_features": "[\"bevy_debug_stepping\", \"bevy_reflect\", \"default\", \"reflect_functions\", \"trace\"]", "target": 288235019596682896, "profile": 1058352400131300129, "path": 3329262467494459322, "deps": [[4064098156404486754, "bevy_tasks", false, 14589856431419027422], [6214442870082674230, "bevy_ecs", false, 5188552860272517651], [11434239582363224126, "downcast_rs", false, 16571129334598469010], [13487854193495724092, "derive_more", false, 4695521678521511335], [13543630808651616527, "bevy_utils", false, 14857909225077134228], [13856126470196444163, "bevy_derive", false, 6599095495235222296], [15953891987145646537, "bevy_reflect", false, 11241836732094182038], [17395637519455072168, "ctrlc", false, 12756679464655206917]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/bevy_app-418077d283f7040d/dep-lib-bevy_app", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}