{"rustc": 15497389221046826682, "features": "[\"bevy_reflect\", \"curve\", \"default\", \"rand\", \"serialize\"]", "declared_features": "[\"approx\", \"bevy_reflect\", \"curve\", \"debug_glam_assert\", \"default\", \"glam_assert\", \"libm\", \"mint\", \"rand\", \"serialize\"]", "target": 18389758704779971090, "profile": 1058352400131300129, "path": 3267518287668169047, "deps": [[2831851536307977279, "glam", false, 13533547507840710799], [3317542222502007281, "itertools", false, 1116430398668935132], [3666196340704888985, "smallvec", false, 9504253755919155840], [9196727883430091646, "rand_distr", false, 7228223375242115467], [9689903380558560274, "serde", false, 3061191315961435077], [13208667028893622512, "rand", false, 10441056979171737511], [13487854193495724092, "derive_more", false, 4695521678521511335], [15953891987145646537, "bevy_reflect", false, 11241836732094182038]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/bevy_math-525880a737e83462/dep-lib-bevy_math", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}