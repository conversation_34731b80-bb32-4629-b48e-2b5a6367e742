{"rustc": 15497389221046826682, "features": "[\"arrayvec\", \"bevy_reflect\", \"default\", \"multi_threaded\", \"serialize\"]", "declared_features": "[\"arrayvec\", \"bevy_debug_stepping\", \"bevy_reflect\", \"default\", \"detailed_trace\", \"multi_threaded\", \"reflect_functions\", \"serialize\", \"trace\", \"track_change_detection\"]", "target": 6640942296801946140, "profile": 1058352400131300129, "path": 16695109006893347096, "deps": [[2425088982514975140, "bevy_ptr", false, 17020633480272987158], [3666196340704888985, "smallvec", false, 9504253755919155840], [4064098156404486754, "bevy_tasks", false, 14589856431419027422], [6234078840545730324, "fixedbitset", false, 1945707298576283063], [7896293946984509699, "bitflags", false, 9669528930193035680], [9018292148362813592, "bevy_ecs_macros", false, 7170313593679258387], [9687787840817006220, "nonmax", false, 16813407222429374200], [9689903380558560274, "serde", false, 3061191315961435077], [12100481297174703255, "concurrent_queue", false, 16975494872311476717], [13487854193495724092, "derive_more", false, 4695521678521511335], [13543630808651616527, "bevy_utils", false, 14857909225077134228], [13847662864258534762, "arrayvec", false, 4509246620592402640], [15918101059100394961, "disqualified", false, 5436175543961816237], [15953891987145646537, "bevy_reflect", false, 11241836732094182038], [16532555906320553198, "petgraph", false, 5303883896225314858]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/bevy_ecs-774b17fdb2a673bb/dep-lib-bevy_ecs", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}