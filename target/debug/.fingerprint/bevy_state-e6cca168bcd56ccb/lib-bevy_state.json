{"rustc": 15497389221046826682, "features": "[\"bevy_app\", \"bevy_hierarchy\", \"bevy_reflect\", \"default\"]", "declared_features": "[\"bevy_app\", \"bevy_hierarchy\", \"bevy_reflect\", \"default\"]", "target": 4104521996849610945, "profile": 1058352400131300129, "path": 2160350834066085205, "deps": [[5075818945153588980, "bevy_hierarchy", false, 3846849142980585986], [6214442870082674230, "bevy_ecs", false, 5188552860272517651], [8922671035508983237, "bevy_state_macros", false, 10136009371243961094], [13543630808651616527, "bevy_utils", false, 14857909225077134228], [13932235871545406073, "bevy_app", false, 17139858076089145923], [15953891987145646537, "bevy_reflect", false, 11241836732094182038]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/bevy_state-e6cca168bcd56ccb/dep-lib-bevy_state", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}