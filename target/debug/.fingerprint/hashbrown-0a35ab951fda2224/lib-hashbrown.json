{"rustc": 15497389221046826682, "features": "[\"ahash\", \"allocator-api2\", \"default\", \"inline-more\", \"serde\"]", "declared_features": "[\"ahash\", \"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"equivalent\", \"inline-more\", \"nightly\", \"raw\", \"rayon\", \"rkyv\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 9101038166729729440, "profile": 6045031025150363531, "path": 8979737143335350578, "deps": [[966925859616469517, "ahash", false, 15228660146463953336], [9150530836556604396, "allocator_api2", false, 15362710522605176844], [9689903380558560274, "serde", false, 3061191315961435077]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/hashbrown-0a35ab951fda2224/dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}