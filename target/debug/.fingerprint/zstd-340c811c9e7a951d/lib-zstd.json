{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"arrays\", \"bindgen\", \"debug\", \"default\", \"doc-cfg\", \"experimental\", \"fat-lto\", \"legacy\", \"no_asm\", \"pkg-config\", \"thin\", \"thin-lto\", \"wasm\", \"zdict_builder\", \"zstdmt\"]", "target": 13967053409313941148, "profile": 6045031025150363531, "path": 7601676039284366050, "deps": [[15788444815745660356, "zstd_safe", false, 14816639489885878322]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/zstd-340c811c9e7a951d/dep-lib-zstd", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}