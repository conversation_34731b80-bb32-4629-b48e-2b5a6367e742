{"rustc": 15497389221046826682, "features": "[\"bevy_reflect\", \"default\"]", "declared_features": "[\"bevy_reflect\", \"default\", \"serde\", \"serialize\"]", "target": 16043837417850573324, "profile": 1058352400131300129, "path": 15028502087490230901, "deps": [[3571374251074753029, "smol_str", false, 11837335302531907217], [6214442870082674230, "bevy_ecs", false, 5188552860272517651], [12221265588041841346, "bevy_core", false, 12372006074805290114], [13487854193495724092, "derive_more", false, 4695521678521511335], [13543630808651616527, "bevy_utils", false, 14857909225077134228], [13932235871545406073, "bevy_app", false, 17139858076089145923], [15953891987145646537, "bevy_reflect", false, 11241836732094182038], [17516170499218035890, "bevy_math", false, 7273102334612357878]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/bevy_input-1700b06e3f2adf8b/dep-lib-bevy_input", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}