{"$message_type":"diagnostic","message":"unused imports: `AirborneState as SF2AirborneState`, `ButtonInput as SF2ButtonInput`, `CollisionConfig`, `CountdownTimer`, `Direction as SF2Direction`, `FbDirection`, `FightMode`, `FighterMode`, `FighterState as SF2<PERSON><PERSON><PERSON>State`, `FighterSubState`, `Fixed16_16`, `Fixed8_8`, `FrameBudgetManager`, `FrameInputBuffer`, `FrameTimer`, `GameMode`, `GameState as SF2GameState`, `InputConfig`, `InputDirection`, `Point16`, `Projectile`, `Rect8`, `RoundMode`, `SpecialMoveDetector`, `SpecialMovePattern`, `StateTransition`, `StateValidator`, and `Vect16`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/lib.rs","byte_start":248,"byte_end":258,"line_start":10,"line_end":10,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, Point16, Vect16, Rect8,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":260,"byte_end":268,"line_start":10,"line_end":10,"column_start":17,"column_end":25,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, Point16, Vect16, Rect8,","highlight_start":17,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":270,"byte_end":277,"line_start":10,"line_end":10,"column_start":27,"column_end":34,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, Point16, Vect16, Rect8,","highlight_start":27,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":279,"byte_end":285,"line_start":10,"line_end":10,"column_start":36,"column_end":42,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, Point16, Vect16, Rect8,","highlight_start":36,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":287,"byte_end":292,"line_start":10,"line_end":10,"column_start":44,"column_end":49,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, Point16, Vect16, Rect8,","highlight_start":44,"highlight_end":49}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":298,"byte_end":308,"line_start":11,"line_end":11,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    Projectile,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":332,"byte_end":363,"line_start":12,"line_end":12,"column_start":23,"column_end":54,"is_primary":true,"text":[{"text":"    FighterStateData, FighterState as SF2FighterState, FighterSubState, FighterMode,","highlight_start":23,"highlight_end":54}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":365,"byte_end":380,"line_start":12,"line_end":12,"column_start":56,"column_end":71,"is_primary":true,"text":[{"text":"    FighterStateData, FighterState as SF2FighterState, FighterSubState, FighterMode,","highlight_start":56,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":382,"byte_end":393,"line_start":12,"line_end":12,"column_start":73,"column_end":84,"is_primary":true,"text":[{"text":"    FighterStateData, FighterState as SF2FighterState, FighterSubState, FighterMode,","highlight_start":73,"highlight_end":84}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":399,"byte_end":407,"line_start":13,"line_end":13,"column_start":5,"column_end":13,"is_primary":true,"text":[{"text":"    GameMode, FightMode, RoundMode, GameState as SF2GameState,","highlight_start":5,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":409,"byte_end":418,"line_start":13,"line_end":13,"column_start":15,"column_end":24,"is_primary":true,"text":[{"text":"    GameMode, FightMode, RoundMode, GameState as SF2GameState,","highlight_start":15,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":420,"byte_end":429,"line_start":13,"line_end":13,"column_start":26,"column_end":35,"is_primary":true,"text":[{"text":"    GameMode, FightMode, RoundMode, GameState as SF2GameState,","highlight_start":26,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":431,"byte_end":456,"line_start":13,"line_end":13,"column_start":37,"column_end":62,"is_primary":true,"text":[{"text":"    GameMode, FightMode, RoundMode, GameState as SF2GameState,","highlight_start":37,"highlight_end":62}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":462,"byte_end":472,"line_start":14,"line_end":14,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    FrameTimer, FrameBudgetManager, CountdownTimer,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":474,"byte_end":492,"line_start":14,"line_end":14,"column_start":17,"column_end":35,"is_primary":true,"text":[{"text":"    FrameTimer, FrameBudgetManager, CountdownTimer,","highlight_start":17,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":494,"byte_end":508,"line_start":14,"line_end":14,"column_start":37,"column_end":51,"is_primary":true,"text":[{"text":"    FrameTimer, FrameBudgetManager, CountdownTimer,","highlight_start":37,"highlight_end":51}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":514,"byte_end":528,"line_start":15,"line_end":15,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"    StateValidator, StateTransition,","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":530,"byte_end":545,"line_start":15,"line_end":15,"column_start":21,"column_end":36,"is_primary":true,"text":[{"text":"    StateValidator, StateTransition,","highlight_start":21,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":562,"byte_end":587,"line_start":16,"line_end":16,"column_start":16,"column_end":41,"is_primary":true,"text":[{"text":"    FighterId, Direction as SF2Direction, AirborneState as SF2AirborneState,","highlight_start":16,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":589,"byte_end":622,"line_start":16,"line_end":16,"column_start":43,"column_end":76,"is_primary":true,"text":[{"text":"    FighterId, Direction as SF2Direction, AirborneState as SF2AirborneState,","highlight_start":43,"highlight_end":76}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":628,"byte_end":657,"line_start":17,"line_end":17,"column_start":5,"column_end":34,"is_primary":true,"text":[{"text":"    ButtonInput as SF2ButtonInput, InputDirection, SpecialMovePattern, FbDirection,","highlight_start":5,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":659,"byte_end":673,"line_start":17,"line_end":17,"column_start":36,"column_end":50,"is_primary":true,"text":[{"text":"    ButtonInput as SF2ButtonInput, InputDirection, SpecialMovePattern, FbDirection,","highlight_start":36,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":675,"byte_end":693,"line_start":17,"line_end":17,"column_start":52,"column_end":70,"is_primary":true,"text":[{"text":"    ButtonInput as SF2ButtonInput, InputDirection, SpecialMovePattern, FbDirection,","highlight_start":52,"highlight_end":70}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":695,"byte_end":706,"line_start":17,"line_end":17,"column_start":72,"column_end":83,"is_primary":true,"text":[{"text":"    ButtonInput as SF2ButtonInput, InputDirection, SpecialMovePattern, FbDirection,","highlight_start":72,"highlight_end":83}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":712,"byte_end":728,"line_start":18,"line_end":18,"column_start":5,"column_end":21,"is_primary":true,"text":[{"text":"    FrameInputBuffer, SpecialMoveDetector, InputConfig,","highlight_start":5,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":730,"byte_end":749,"line_start":18,"line_end":18,"column_start":23,"column_end":42,"is_primary":true,"text":[{"text":"    FrameInputBuffer, SpecialMoveDetector, InputConfig,","highlight_start":23,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":751,"byte_end":762,"line_start":18,"line_end":18,"column_start":44,"column_end":55,"is_primary":true,"text":[{"text":"    FrameInputBuffer, SpecialMoveDetector, InputConfig,","highlight_start":44,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":768,"byte_end":783,"line_start":19,"line_end":19,"column_start":5,"column_end":20,"is_primary":true,"text":[{"text":"    CollisionConfig, SpatialGrid, CollisionResponseProcessor, HitboxManager,","highlight_start":5,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/lib.rs","byte_start":248,"byte_end":314,"line_start":10,"line_end":12,"column_start":5,"column_end":5,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, Point16, Vect16, Rect8,","highlight_start":5,"highlight_end":50},{"text":"    Projectile,","highlight_start":1,"highlight_end":16},{"text":"    FighterStateData, FighterState as SF2FighterState, FighterSubState, FighterMode,","highlight_start":1,"highlight_end":5}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":330,"byte_end":545,"line_start":12,"line_end":15,"column_start":21,"column_end":36,"is_primary":true,"text":[{"text":"    FighterStateData, FighterState as SF2FighterState, FighterSubState, FighterMode,","highlight_start":21,"highlight_end":85},{"text":"    GameMode, FightMode, RoundMode, GameState as SF2GameState,","highlight_start":1,"highlight_end":63},{"text":"    FrameTimer, FrameBudgetManager, CountdownTimer,","highlight_start":1,"highlight_end":52},{"text":"    StateValidator, StateTransition,","highlight_start":1,"highlight_end":36}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":560,"byte_end":783,"line_start":16,"line_end":19,"column_start":14,"column_end":20,"is_primary":true,"text":[{"text":"    FighterId, Direction as SF2Direction, AirborneState as SF2AirborneState,","highlight_start":14,"highlight_end":77},{"text":"    ButtonInput as SF2ButtonInput, InputDirection, SpecialMovePattern, FbDirection,","highlight_start":1,"highlight_end":84},{"text":"    FrameInputBuffer, SpecialMoveDetector, InputConfig,","highlight_start":1,"highlight_end":56},{"text":"    CollisionConfig, SpatialGrid, CollisionResponseProcessor, HitboxManager,","highlight_start":1,"highlight_end":20}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `AirborneState as SF2AirborneState`, `ButtonInput as SF2ButtonInput`, `CollisionConfig`, `CountdownTimer`, `Direction as SF2Direction`, `FbDirection`, `FightMode`, `FighterMode`, `FighterState as SF2FighterState`, `FighterSubState`, `Fixed16_16`, `Fixed8_8`, `FrameBudgetManager`, `FrameInputBuffer`, `FrameTimer`, `GameMode`, `GameState as SF2GameState`, `InputConfig`, `InputDirection`, `Point16`, `Projectile`, `Rect8`, `RoundMode`, `SpecialMoveDetector`, `SpecialMovePattern`, `StateTransition`, `StateValidator`, and `Vect16`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/lib.rs:10:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Fixed16_16, Fixed8_8, Point16, Vect16, Rect8,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Projectile,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    FighterStateData, FighterState as SF2FighterState, FighterSubState, FighterMode,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                       \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    GameMode, FightMode, RoundMode, GameState as SF2GameState,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    FrameTimer, FrameBudgetManager, CountdownTimer,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    StateValidator, StateTransition,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    FighterId, Direction as SF2Direction, AirborneState as SF2AirborneState,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m17\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    ButtonInput as SF2ButtonInput, InputDirection, SpecialMovePattern, FbDirection,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m18\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    FrameInputBuffer, SpecialMoveDetector, InputConfig,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m19\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    CollisionConfig, SpatialGrid, CollisionResponseProcessor, HitboxManager,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"private item shadows public glob re-export","code":{"code":"hidden_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/lib.rs","byte_start":298,"byte_end":308,"line_start":11,"line_end":11,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    Projectile,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the name `Projectile` in the type namespace is supposed to be publicly re-exported here","code":null,"level":"note","spans":[{"file_name":"sf2_engine/src/lib.rs","byte_start":1067,"byte_end":1080,"line_start":34,"line_end":34,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use components::*;","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"but the private item here shadows it","code":null,"level":"note","spans":[{"file_name":"sf2_engine/src/lib.rs","byte_start":298,"byte_end":308,"line_start":11,"line_end":11,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    Projectile,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"`#[warn(hidden_glob_reexports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: private item shadows public glob re-export\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/lib.rs:11:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Projectile,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: the name `Projectile` in the type namespace is supposed to be publicly re-exported here\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/lib.rs:34:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m34\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use components::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: but the private item here shadows it\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/lib.rs:11:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Projectile,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(hidden_glob_reexports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `FbDirection`, `FrameInputBuffer`, `InputConfig`, `SpecialMoveDetector`, and `SpecialMovePattern`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/systems.rs","byte_start":269,"byte_end":287,"line_start":10,"line_end":10,"column_start":36,"column_end":54,"is_primary":true,"text":[{"text":"    ButtonInput as SF2ButtonInput, SpecialMovePattern, FbDirection,","highlight_start":36,"highlight_end":54}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/systems.rs","byte_start":289,"byte_end":300,"line_start":10,"line_end":10,"column_start":56,"column_end":67,"is_primary":true,"text":[{"text":"    ButtonInput as SF2ButtonInput, SpecialMovePattern, FbDirection,","highlight_start":56,"highlight_end":67}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/systems.rs","byte_start":306,"byte_end":322,"line_start":11,"line_end":11,"column_start":5,"column_end":21,"is_primary":true,"text":[{"text":"    FrameInputBuffer, SpecialMoveDetector, InputConfig,","highlight_start":5,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/systems.rs","byte_start":324,"byte_end":343,"line_start":11,"line_end":11,"column_start":23,"column_end":42,"is_primary":true,"text":[{"text":"    FrameInputBuffer, SpecialMoveDetector, InputConfig,","highlight_start":23,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/systems.rs","byte_start":345,"byte_end":356,"line_start":11,"line_end":11,"column_start":44,"column_end":55,"is_primary":true,"text":[{"text":"    FrameInputBuffer, SpecialMoveDetector, InputConfig,","highlight_start":44,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/systems.rs","byte_start":267,"byte_end":356,"line_start":10,"line_end":11,"column_start":34,"column_end":55,"is_primary":true,"text":[{"text":"    ButtonInput as SF2ButtonInput, SpecialMovePattern, FbDirection,","highlight_start":34,"highlight_end":68},{"text":"    FrameInputBuffer, SpecialMoveDetector, InputConfig,","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `FbDirection`, `FrameInputBuffer`, `InputConfig`, `SpecialMoveDetector`, and `SpecialMovePattern`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/systems.rs:10:36\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    ButtonInput as SF2ButtonInput, SpecialMovePattern, FbDirection,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    FrameInputBuffer, SpecialMoveDetector, InputConfig,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `FightMode`, `FighterMode`, `FighterSubState`, `Fixed8_8`, `GameMode`, `Point16`, `StateTransition`, `StateValidator`, and `Vect16`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/game_logic.rs","byte_start":283,"byte_end":298,"line_start":8,"line_end":8,"column_start":56,"column_end":71,"is_primary":true,"text":[{"text":"    FighterStateData, FighterState as SF2FighterState, FighterSubState, FighterMode,","highlight_start":56,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/game_logic.rs","byte_start":300,"byte_end":311,"line_start":8,"line_end":8,"column_start":73,"column_end":84,"is_primary":true,"text":[{"text":"    FighterStateData, FighterState as SF2FighterState, FighterSubState, FighterMode,","highlight_start":73,"highlight_end":84}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/game_logic.rs","byte_start":317,"byte_end":325,"line_start":9,"line_end":9,"column_start":5,"column_end":13,"is_primary":true,"text":[{"text":"    GameMode, FightMode, RoundMode, GameState as SF2GameState, SubMode, AnimMode, AudioMode, ExtendedMode,","highlight_start":5,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/game_logic.rs","byte_start":327,"byte_end":336,"line_start":9,"line_end":9,"column_start":15,"column_end":24,"is_primary":true,"text":[{"text":"    GameMode, FightMode, RoundMode, GameState as SF2GameState, SubMode, AnimMode, AudioMode, ExtendedMode,","highlight_start":15,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/game_logic.rs","byte_start":476,"byte_end":490,"line_start":11,"line_end":11,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"    StateValidator, StateTransition, FighterStateTransition,","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/game_logic.rs","byte_start":492,"byte_end":507,"line_start":11,"line_end":11,"column_start":21,"column_end":36,"is_primary":true,"text":[{"text":"    StateValidator, StateTransition, FighterStateTransition,","highlight_start":21,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/game_logic.rs","byte_start":549,"byte_end":557,"line_start":12,"line_end":12,"column_start":17,"column_end":25,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, Point16, Vect16,","highlight_start":17,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/game_logic.rs","byte_start":559,"byte_end":566,"line_start":12,"line_end":12,"column_start":27,"column_end":34,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, Point16, Vect16,","highlight_start":27,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/game_logic.rs","byte_start":568,"byte_end":574,"line_start":12,"line_end":12,"column_start":36,"column_end":42,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, Point16, Vect16,","highlight_start":36,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/game_logic.rs","byte_start":281,"byte_end":336,"line_start":8,"line_end":9,"column_start":54,"column_end":24,"is_primary":true,"text":[{"text":"    FighterStateData, FighterState as SF2FighterState, FighterSubState, FighterMode,","highlight_start":54,"highlight_end":85},{"text":"    GameMode, FightMode, RoundMode, GameState as SF2GameState, SubMode, AnimMode, AudioMode, ExtendedMode,","highlight_start":1,"highlight_end":24}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"sf2_engine/src/game_logic.rs","byte_start":470,"byte_end":507,"line_start":10,"line_end":11,"column_start":51,"column_end":36,"is_primary":true,"text":[{"text":"    FrameTimer, FrameBudgetManager, CountdownTimer,","highlight_start":51,"highlight_end":52},{"text":"    StateValidator, StateTransition, FighterStateTransition,","highlight_start":1,"highlight_end":36}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"sf2_engine/src/game_logic.rs","byte_start":547,"byte_end":574,"line_start":12,"line_end":12,"column_start":15,"column_end":42,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, Point16, Vect16,","highlight_start":15,"highlight_end":42}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `FightMode`, `FighterMode`, `FighterSubState`, `Fixed8_8`, `GameMode`, `Point16`, `StateTransition`, `StateValidator`, and `Vect16`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/game_logic.rs:8:56\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    FighterStateData, FighterState as SF2FighterState, FighterSubState, FighterMode,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m9\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    GameMode, FightMode, RoundMode, GameState as SF2GameState, SubMode, AnimMode, AudioMode, ExtendedMode,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    FrameTimer, FrameBudgetManager, CountdownTimer,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    StateValidator, StateTransition, FighterStateTransition,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Fixed16_16, Fixed8_8, Point16, Vect16,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `bevy::ecs::query::QueryFilter`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":227,"byte_end":256,"line_start":7,"line_end":7,"column_start":5,"column_end":34,"is_primary":true,"text":[{"text":"use bevy::ecs::query::QueryFilter;","highlight_start":5,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":223,"byte_end":258,"line_start":7,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use bevy::ecs::query::QueryFilter;","highlight_start":1,"highlight_end":35},{"text":"use bevy::ecs::system::SystemParam;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `bevy::ecs::query::QueryFilter`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/performance.rs:7:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse bevy::ecs::query::QueryFilter;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::marker::PhantomData`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":298,"byte_end":322,"line_start":9,"line_end":9,"column_start":5,"column_end":29,"is_primary":true,"text":[{"text":"use std::marker::PhantomData;","highlight_start":5,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":294,"byte_end":324,"line_start":9,"line_end":10,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::marker::PhantomData;","highlight_start":1,"highlight_end":30},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `std::marker::PhantomData`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/performance.rs:9:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::marker::PhantomData;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `CountdownTimer`, `FighterMode`, `FighterSubState`, `Fixed8_8`, `FrameBudgetManager`, `FrameTimer`, `Point16`, and `Vect16`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":397,"byte_end":412,"line_start":12,"line_end":12,"column_start":56,"column_end":71,"is_primary":true,"text":[{"text":"    FighterStateData, FighterState as SF2FighterState, FighterSubState, FighterMode,","highlight_start":56,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/performance.rs","byte_start":414,"byte_end":425,"line_start":12,"line_end":12,"column_start":73,"column_end":84,"is_primary":true,"text":[{"text":"    FighterStateData, FighterState as SF2FighterState, FighterSubState, FighterMode,","highlight_start":73,"highlight_end":84}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/performance.rs","byte_start":431,"byte_end":441,"line_start":13,"line_end":13,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    FrameTimer, FrameBudgetManager, CountdownTimer, FighterStateTransition,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/performance.rs","byte_start":443,"byte_end":461,"line_start":13,"line_end":13,"column_start":17,"column_end":35,"is_primary":true,"text":[{"text":"    FrameTimer, FrameBudgetManager, CountdownTimer, FighterStateTransition,","highlight_start":17,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/performance.rs","byte_start":463,"byte_end":477,"line_start":13,"line_end":13,"column_start":37,"column_end":51,"is_primary":true,"text":[{"text":"    FrameTimer, FrameBudgetManager, CountdownTimer, FighterStateTransition,","highlight_start":37,"highlight_end":51}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/performance.rs","byte_start":519,"byte_end":527,"line_start":14,"line_end":14,"column_start":17,"column_end":25,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, Point16, Vect16,","highlight_start":17,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/performance.rs","byte_start":529,"byte_end":536,"line_start":14,"line_end":14,"column_start":27,"column_end":34,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, Point16, Vect16,","highlight_start":27,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/performance.rs","byte_start":538,"byte_end":544,"line_start":14,"line_end":14,"column_start":36,"column_end":42,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, Point16, Vect16,","highlight_start":36,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":395,"byte_end":477,"line_start":12,"line_end":13,"column_start":54,"column_end":51,"is_primary":true,"text":[{"text":"    FighterStateData, FighterState as SF2FighterState, FighterSubState, FighterMode,","highlight_start":54,"highlight_end":85},{"text":"    FrameTimer, FrameBudgetManager, CountdownTimer, FighterStateTransition,","highlight_start":1,"highlight_end":51}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"sf2_engine/src/performance.rs","byte_start":517,"byte_end":544,"line_start":14,"line_end":14,"column_start":15,"column_end":42,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, Point16, Vect16,","highlight_start":15,"highlight_end":42}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `CountdownTimer`, `FighterMode`, `FighterSubState`, `Fixed8_8`, `FrameBudgetManager`, `FrameTimer`, `Point16`, and `Vect16`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/performance.rs:12:56\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    FighterStateData, FighterState as SF2FighterState, FighterSubState, FighterMode,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    FrameTimer, FrameBudgetManager, CountdownTimer, FighterStateTransition,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Fixed16_16, Fixed8_8, Point16, Vect16,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `ActiveHitbox`, `CollisionDetector`, `Fixed8_8`, `GridCell`, `Hurtbox`, and `Pushbox`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/collision_system.rs","byte_start":303,"byte_end":320,"line_start":11,"line_end":11,"column_start":22,"column_end":39,"is_primary":true,"text":[{"text":"    CollisionConfig, CollisionDetector, SF2CollisionChecker, CollisionResult,","highlight_start":22,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/collision_system.rs","byte_start":407,"byte_end":415,"line_start":12,"line_end":12,"column_start":48,"column_end":56,"is_primary":true,"text":[{"text":"    SpatialGrid, SpatialEntry, CollisionLayer, GridCell,","highlight_start":48,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/collision_system.rs","byte_start":453,"byte_end":465,"line_start":13,"line_end":13,"column_start":37,"column_end":49,"is_primary":true,"text":[{"text":"    HitboxManager, FighterHitboxes, ActiveHitbox, Hurtbox, Pushbox,","highlight_start":37,"highlight_end":49}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/collision_system.rs","byte_start":467,"byte_end":474,"line_start":13,"line_end":13,"column_start":51,"column_end":58,"is_primary":true,"text":[{"text":"    HitboxManager, FighterHitboxes, ActiveHitbox, Hurtbox, Pushbox,","highlight_start":51,"highlight_end":58}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/collision_system.rs","byte_start":476,"byte_end":483,"line_start":13,"line_end":13,"column_start":60,"column_end":67,"is_primary":true,"text":[{"text":"    HitboxManager, FighterHitboxes, ActiveHitbox, Hurtbox, Pushbox,","highlight_start":60,"highlight_end":67}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/collision_system.rs","byte_start":587,"byte_end":595,"line_start":15,"line_end":15,"column_start":29,"column_end":37,"is_primary":true,"text":[{"text":"    Point16, Point8, Rect8, Fixed8_8,","highlight_start":29,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/collision_system.rs","byte_start":301,"byte_end":320,"line_start":11,"line_end":11,"column_start":20,"column_end":39,"is_primary":true,"text":[{"text":"    CollisionConfig, CollisionDetector, SF2CollisionChecker, CollisionResult,","highlight_start":20,"highlight_end":39}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"sf2_engine/src/collision_system.rs","byte_start":405,"byte_end":415,"line_start":12,"line_end":12,"column_start":46,"column_end":56,"is_primary":true,"text":[{"text":"    SpatialGrid, SpatialEntry, CollisionLayer, GridCell,","highlight_start":46,"highlight_end":56}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"sf2_engine/src/collision_system.rs","byte_start":451,"byte_end":483,"line_start":13,"line_end":13,"column_start":35,"column_end":67,"is_primary":true,"text":[{"text":"    HitboxManager, FighterHitboxes, ActiveHitbox, Hurtbox, Pushbox,","highlight_start":35,"highlight_end":67}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"sf2_engine/src/collision_system.rs","byte_start":585,"byte_end":595,"line_start":15,"line_end":15,"column_start":27,"column_end":37,"is_primary":true,"text":[{"text":"    Point16, Point8, Rect8, Fixed8_8,","highlight_start":27,"highlight_end":37}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `ActiveHitbox`, `CollisionDetector`, `Fixed8_8`, `GridCell`, `Hurtbox`, and `Pushbox`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/collision_system.rs:11:22\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    CollisionConfig, CollisionDetector, SF2CollisionChecker, CollisionResult,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    SpatialGrid, SpatialEntry, CollisionLayer, GridCell,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    HitboxManager, FighterHitboxes, ActiveHitbox, Hurtbox, Pushbox,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    CollisionResponseProcessor, CollisionResponse, CollisionResponseType,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Point16, Point8, Rect8, Fixed8_8,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `ButtonFlags`, `InputDirection`, and `InputState`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/character_system.rs","byte_start":310,"byte_end":320,"line_start":9,"line_end":9,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    InputState, ButtonFlags, InputDirection,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/character_system.rs","byte_start":322,"byte_end":333,"line_start":9,"line_end":9,"column_start":17,"column_end":28,"is_primary":true,"text":[{"text":"    InputState, ButtonFlags, InputDirection,","highlight_start":17,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/character_system.rs","byte_start":335,"byte_end":349,"line_start":9,"line_end":9,"column_start":30,"column_end":44,"is_primary":true,"text":[{"text":"    InputState, ButtonFlags, InputDirection,","highlight_start":30,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/character_system.rs","byte_start":304,"byte_end":349,"line_start":8,"line_end":9,"column_start":71,"column_end":44,"is_primary":true,"text":[{"text":"    FighterId, FighterStateData, Fixed16_16, Fixed8_8, Point16, Vect16,","highlight_start":71,"highlight_end":72},{"text":"    InputState, ButtonFlags, InputDirection,","highlight_start":1,"highlight_end":44}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `ButtonFlags`, `InputDirection`, and `InputState`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/character_system.rs:9:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    InputState, ButtonFlags, InputDirection,\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `bevy::prelude::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":166,"byte_end":182,"line_start":6,"line_end":6,"column_start":5,"column_end":21,"is_primary":true,"text":[{"text":"use bevy::prelude::*;","highlight_start":5,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":162,"byte_end":184,"line_start":6,"line_end":7,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use bevy::prelude::*;","highlight_start":1,"highlight_end":22},{"text":"use sf2_types::{","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `bevy::prelude::*`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ryu.rs:6:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse bevy::prelude::*;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `ButtonFlags` and `Fixed16_16`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":255,"byte_end":265,"line_start":9,"line_end":9,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, InputState, ButtonFlags, InputDirection, ButtonInput,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":289,"byte_end":300,"line_start":9,"line_end":9,"column_start":39,"column_end":50,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, InputState, ButtonFlags, InputDirection, ButtonInput,","highlight_start":39,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":249,"byte_end":265,"line_start":8,"line_end":9,"column_start":49,"column_end":15,"is_primary":true,"text":[{"text":"    FighterId, FighterStateData, Point16, Vect16,","highlight_start":49,"highlight_end":50},{"text":"    Fixed16_16, Fixed8_8, InputState, ButtonFlags, InputDirection, ButtonInput,","highlight_start":1,"highlight_end":15}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":287,"byte_end":300,"line_start":9,"line_end":9,"column_start":37,"column_end":50,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, InputState, ButtonFlags, InputDirection, ButtonInput,","highlight_start":37,"highlight_end":50}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `ButtonFlags` and `Fixed16_16`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ryu.rs:9:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Fixed16_16, Fixed8_8, InputState, ButtonFlags, InputDirection, ButtonInput,\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `bevy::prelude::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":178,"byte_end":194,"line_start":6,"line_end":6,"column_start":5,"column_end":21,"is_primary":true,"text":[{"text":"use bevy::prelude::*;","highlight_start":5,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":174,"byte_end":196,"line_start":6,"line_end":7,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use bevy::prelude::*;","highlight_start":1,"highlight_end":22},{"text":"use sf2_types::{","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `bevy::prelude::*`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ken.rs:6:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse bevy::prelude::*;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `ButtonFlags` and `Fixed16_16`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":267,"byte_end":277,"line_start":9,"line_end":9,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, InputState, ButtonFlags, InputDirection, ButtonInput,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":301,"byte_end":312,"line_start":9,"line_end":9,"column_start":39,"column_end":50,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, InputState, ButtonFlags, InputDirection, ButtonInput,","highlight_start":39,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":261,"byte_end":277,"line_start":8,"line_end":9,"column_start":49,"column_end":15,"is_primary":true,"text":[{"text":"    FighterId, FighterStateData, Point16, Vect16,","highlight_start":49,"highlight_end":50},{"text":"    Fixed16_16, Fixed8_8, InputState, ButtonFlags, InputDirection, ButtonInput,","highlight_start":1,"highlight_end":15}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":299,"byte_end":312,"line_start":9,"line_end":9,"column_start":37,"column_end":50,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, InputState, ButtonFlags, InputDirection, ButtonInput,","highlight_start":37,"highlight_end":50}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `ButtonFlags` and `Fixed16_16`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ken.rs:9:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Fixed16_16, Fixed8_8, InputState, ButtonFlags, InputDirection, ButtonInput,\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `bevy::prelude::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/chun_li.rs","byte_start":192,"byte_end":208,"line_start":6,"line_end":6,"column_start":5,"column_end":21,"is_primary":true,"text":[{"text":"use bevy::prelude::*;","highlight_start":5,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/chun_li.rs","byte_start":188,"byte_end":210,"line_start":6,"line_end":7,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use bevy::prelude::*;","highlight_start":1,"highlight_end":22},{"text":"use sf2_types::{","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `bevy::prelude::*`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/chun_li.rs:6:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse bevy::prelude::*;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `ButtonFlags`, `ButtonInput`, `Fixed16_16`, and `InputDirection`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/chun_li.rs","byte_start":231,"byte_end":241,"line_start":8,"line_end":8,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, InputState, ButtonFlags, InputDirection, ButtonInput,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/characters/chun_li.rs","byte_start":265,"byte_end":276,"line_start":8,"line_end":8,"column_start":39,"column_end":50,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, InputState, ButtonFlags, InputDirection, ButtonInput,","highlight_start":39,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/characters/chun_li.rs","byte_start":278,"byte_end":292,"line_start":8,"line_end":8,"column_start":52,"column_end":66,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, InputState, ButtonFlags, InputDirection, ButtonInput,","highlight_start":52,"highlight_end":66}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/characters/chun_li.rs","byte_start":294,"byte_end":305,"line_start":8,"line_end":8,"column_start":68,"column_end":79,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, InputState, ButtonFlags, InputDirection, ButtonInput,","highlight_start":68,"highlight_end":79}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/chun_li.rs","byte_start":231,"byte_end":243,"line_start":8,"line_end":8,"column_start":5,"column_end":17,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, InputState, ButtonFlags, InputDirection, ButtonInput,","highlight_start":5,"highlight_end":17}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"sf2_engine/src/characters/chun_li.rs","byte_start":263,"byte_end":305,"line_start":8,"line_end":8,"column_start":37,"column_end":79,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, InputState, ButtonFlags, InputDirection, ButtonInput,","highlight_start":37,"highlight_end":79}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `ButtonFlags`, `ButtonInput`, `Fixed16_16`, and `InputDirection`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/chun_li.rs:8:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Fixed16_16, Fixed8_8, InputState, ButtonFlags, InputDirection, ButtonInput,\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `bevy::prelude::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/guile.rs","byte_start":194,"byte_end":210,"line_start":6,"line_end":6,"column_start":5,"column_end":21,"is_primary":true,"text":[{"text":"use bevy::prelude::*;","highlight_start":5,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/guile.rs","byte_start":190,"byte_end":212,"line_start":6,"line_end":7,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use bevy::prelude::*;","highlight_start":1,"highlight_end":22},{"text":"use sf2_types::{","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `bevy::prelude::*`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/guile.rs:6:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse bevy::prelude::*;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `ButtonFlags`, `ButtonInput`, `FighterId`, and `Fixed16_16`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/guile.rs","byte_start":233,"byte_end":243,"line_start":8,"line_end":8,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, InputState, ButtonFlags, InputDirection, ButtonInput,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/characters/guile.rs","byte_start":267,"byte_end":278,"line_start":8,"line_end":8,"column_start":39,"column_end":50,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, InputState, ButtonFlags, InputDirection, ButtonInput,","highlight_start":39,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/characters/guile.rs","byte_start":296,"byte_end":307,"line_start":8,"line_end":8,"column_start":68,"column_end":79,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, InputState, ButtonFlags, InputDirection, ButtonInput,","highlight_start":68,"highlight_end":79}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/characters/guile.rs","byte_start":369,"byte_end":378,"line_start":9,"line_end":9,"column_start":61,"column_end":70,"is_primary":true,"text":[{"text":"    FighterStateData, FighterState, Point16, Vect16, Rect8, FighterId, Point8, Size8,","highlight_start":61,"highlight_end":70}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/guile.rs","byte_start":233,"byte_end":245,"line_start":8,"line_end":8,"column_start":5,"column_end":17,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, InputState, ButtonFlags, InputDirection, ButtonInput,","highlight_start":5,"highlight_end":17}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"sf2_engine/src/characters/guile.rs","byte_start":265,"byte_end":278,"line_start":8,"line_end":8,"column_start":37,"column_end":50,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, InputState, ButtonFlags, InputDirection, ButtonInput,","highlight_start":37,"highlight_end":50}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"sf2_engine/src/characters/guile.rs","byte_start":294,"byte_end":307,"line_start":8,"line_end":8,"column_start":66,"column_end":79,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, InputState, ButtonFlags, InputDirection, ButtonInput,","highlight_start":66,"highlight_end":79}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"sf2_engine/src/characters/guile.rs","byte_start":367,"byte_end":378,"line_start":9,"line_end":9,"column_start":59,"column_end":70,"is_primary":true,"text":[{"text":"    FighterStateData, FighterState, Point16, Vect16, Rect8, FighterId, Point8, Size8,","highlight_start":59,"highlight_end":70}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `ButtonFlags`, `ButtonInput`, `FighterId`, and `Fixed16_16`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/guile.rs:8:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Fixed16_16, Fixed8_8, InputState, ButtonFlags, InputDirection, ButtonInput,\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    FighterStateData, FighterState, Point16, Vect16, Rect8, FighterId, Point8, Size8,\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `sf2_types::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_debug.rs","byte_start":303,"byte_end":315,"line_start":8,"line_end":8,"column_start":5,"column_end":17,"is_primary":true,"text":[{"text":"use sf2_types::*;","highlight_start":5,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_debug.rs","byte_start":299,"byte_end":317,"line_start":8,"line_end":9,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use sf2_types::*;","highlight_start":1,"highlight_end":18},{"text":"use crate::ai::ai_core::*;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `sf2_types::*`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_debug.rs:8:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse sf2_types::*;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::ai::ai_strategy::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_debug.rs","byte_start":348,"byte_end":373,"line_start":10,"line_end":10,"column_start":5,"column_end":30,"is_primary":true,"text":[{"text":"use crate::ai::ai_strategy::*;","highlight_start":5,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_debug.rs","byte_start":344,"byte_end":375,"line_start":10,"line_end":11,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::ai::ai_strategy::*;","highlight_start":1,"highlight_end":31},{"text":"use crate::ai::ai_difficulty::{AIDifficulty, AIPerformanceMetrics};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `crate::ai::ai_strategy::*`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_debug.rs:10:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::ai::ai_strategy::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::ai::ai_core::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_character_patterns.rs","byte_start":314,"byte_end":335,"line_start":8,"line_end":8,"column_start":5,"column_end":26,"is_primary":true,"text":[{"text":"use crate::ai::ai_core::*;","highlight_start":5,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_character_patterns.rs","byte_start":310,"byte_end":337,"line_start":8,"line_end":9,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::ai::ai_core::*;","highlight_start":1,"highlight_end":27},{"text":"use crate::ai::ai_strategy::*;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `crate::ai::ai_core::*`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_character_patterns.rs:8:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::ai::ai_core::*;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::ai::ai_strategy::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_character_patterns.rs","byte_start":341,"byte_end":366,"line_start":9,"line_end":9,"column_start":5,"column_end":30,"is_primary":true,"text":[{"text":"use crate::ai::ai_strategy::*;","highlight_start":5,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_character_patterns.rs","byte_start":337,"byte_end":368,"line_start":9,"line_end":10,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::ai::ai_strategy::*;","highlight_start":1,"highlight_end":31},{"text":"use crate::ai::ai_difficulty::*;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `crate::ai::ai_strategy::*`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_character_patterns.rs:9:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::ai::ai_strategy::*;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::ai::ai_difficulty::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_character_patterns.rs","byte_start":372,"byte_end":399,"line_start":10,"line_end":10,"column_start":5,"column_end":32,"is_primary":true,"text":[{"text":"use crate::ai::ai_difficulty::*;","highlight_start":5,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_character_patterns.rs","byte_start":368,"byte_end":401,"line_start":10,"line_end":11,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::ai::ai_difficulty::*;","highlight_start":1,"highlight_end":33},{"text":"use crate::ai::ai_behavior_tree::AIAction;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `crate::ai::ai_difficulty::*`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_character_patterns.rs:10:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::ai::ai_difficulty::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::ai::ai_difficulty::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_behavior_tree.rs","byte_start":224,"byte_end":251,"line_start":8,"line_end":8,"column_start":5,"column_end":32,"is_primary":true,"text":[{"text":"use crate::ai::ai_difficulty::*;","highlight_start":5,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_behavior_tree.rs","byte_start":220,"byte_end":253,"line_start":8,"line_end":9,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::ai::ai_difficulty::*;","highlight_start":1,"highlight_end":33},{"text":"use crate::ai::ai_threat::*;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `crate::ai::ai_difficulty::*`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_behavior_tree.rs:8:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::ai::ai_difficulty::*;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::collections::HashMap`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_behavior_tree.rs","byte_start":343,"byte_end":368,"line_start":12,"line_end":12,"column_start":5,"column_end":30,"is_primary":true,"text":[{"text":"use std::collections::HashMap;","highlight_start":5,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_behavior_tree.rs","byte_start":339,"byte_end":370,"line_start":12,"line_end":13,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::collections::HashMap;","highlight_start":1,"highlight_end":31},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `std::collections::HashMap`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_behavior_tree.rs:12:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::collections::HashMap;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `direction` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/systems.rs","byte_start":939,"byte_end":948,"line_start":28,"line_end":28,"column_start":17,"column_end":26,"is_primary":true,"text":[{"text":"        let mut direction = InputDirection::Neutral;","highlight_start":17,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`#[warn(unused_assignments)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: value assigned to `direction` is never read\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/systems.rs:28:17\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m28\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut direction = InputDirection::Neutral;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_assignments)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `buffer_query`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/systems.rs","byte_start":559,"byte_end":571,"line_start":18,"line_end":18,"column_start":9,"column_end":21,"is_primary":true,"text":[{"text":"    mut buffer_query: Query<&mut InputBufferComponent, With<Fighter>>,","highlight_start":9,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/systems.rs","byte_start":559,"byte_end":571,"line_start":18,"line_end":18,"column_start":9,"column_end":21,"is_primary":true,"text":[{"text":"    mut buffer_query: Query<&mut InputBufferComponent, With<Fighter>>,","highlight_start":9,"highlight_end":21}],"label":null,"suggested_replacement":"_buffer_query","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `buffer_query`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/systems.rs:18:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m18\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    mut buffer_query: Query<&mut InputBufferComponent, With<Fighter>>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_buffer_query`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/systems.rs","byte_start":555,"byte_end":571,"line_start":18,"line_end":18,"column_start":5,"column_end":21,"is_primary":true,"text":[{"text":"    mut buffer_query: Query<&mut InputBufferComponent, With<Fighter>>,","highlight_start":5,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_mut)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/systems.rs","byte_start":555,"byte_end":559,"line_start":18,"line_end":18,"column_start":5,"column_end":9,"is_primary":true,"text":[{"text":"    mut buffer_query: Query<&mut InputBufferComponent, With<Fighter>>,","highlight_start":5,"highlight_end":9}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variable does not need to be mutable\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/systems.rs:18:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m18\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    mut buffer_query: Query<&mut InputBufferComponent, With<Fighter>>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mhelp: remove this `mut`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_mut)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `health`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/game_logic.rs","byte_start":5388,"byte_end":5394,"line_start":189,"line_end":189,"column_start":69,"column_end":75,"is_primary":true,"text":[{"text":"    for (entity, mut fighter_state, mut position, mut velocity, mut health, input_buffer) in fighter_query.iter_mut() {","highlight_start":69,"highlight_end":75}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/game_logic.rs","byte_start":5388,"byte_end":5394,"line_start":189,"line_end":189,"column_start":69,"column_end":75,"is_primary":true,"text":[{"text":"    for (entity, mut fighter_state, mut position, mut velocity, mut health, input_buffer) in fighter_query.iter_mut() {","highlight_start":69,"highlight_end":75}],"label":null,"suggested_replacement":"_health","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `health`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/game_logic.rs:189:69\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m189\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    for (entity, mut fighter_state, mut position, mut velocity, mut health, input_buffer) in fighter_query.iter_mut() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_health`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `game_logic`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/game_logic.rs","byte_start":4947,"byte_end":4957,"line_start":176,"line_end":176,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    game_logic: &mut ResMut<GameLogic>,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/game_logic.rs","byte_start":4947,"byte_end":4957,"line_start":176,"line_end":176,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    game_logic: &mut ResMut<GameLogic>,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":"_game_logic","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `game_logic`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/game_logic.rs:176:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m176\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    game_logic: &mut ResMut<GameLogic>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_game_logic`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/game_logic.rs","byte_start":5384,"byte_end":5394,"line_start":189,"line_end":189,"column_start":65,"column_end":75,"is_primary":true,"text":[{"text":"    for (entity, mut fighter_state, mut position, mut velocity, mut health, input_buffer) in fighter_query.iter_mut() {","highlight_start":65,"highlight_end":75}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/game_logic.rs","byte_start":5384,"byte_end":5388,"line_start":189,"line_end":189,"column_start":65,"column_end":69,"is_primary":true,"text":[{"text":"    for (entity, mut fighter_state, mut position, mut velocity, mut health, input_buffer) in fighter_query.iter_mut() {","highlight_start":65,"highlight_end":69}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variable does not need to be mutable\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/game_logic.rs:189:65\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m189\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    for (entity, mut fighter_state, mut position, mut velocity, mut health, input_buffer) in fighter_query.iter_mut() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mhelp: remove this `mut`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/game_logic.rs","byte_start":9034,"byte_end":9051,"line_start":280,"line_end":280,"column_start":13,"column_end":30,"is_primary":true,"text":[{"text":"    for (_, mut fighter_state, _, mut velocity, _, _) in fighter_query.iter_mut() {","highlight_start":13,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/game_logic.rs","byte_start":9034,"byte_end":9038,"line_start":280,"line_end":280,"column_start":13,"column_end":17,"is_primary":true,"text":[{"text":"    for (_, mut fighter_state, _, mut velocity, _, _) in fighter_query.iter_mut() {","highlight_start":13,"highlight_end":17}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variable does not need to be mutable\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/game_logic.rs:280:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m280\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    for (_, mut fighter_state, _, mut velocity, _, _) in fighter_query.iter_mut() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mhelp: remove this `mut`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `hit_events`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/game_logic.rs","byte_start":9690,"byte_end":9700,"line_start":303,"line_end":303,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    hit_events: &mut EventWriter<HitEvent>,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/game_logic.rs","byte_start":9690,"byte_end":9700,"line_start":303,"line_end":303,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    hit_events: &mut EventWriter<HitEvent>,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":"_hit_events","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `hit_events`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/game_logic.rs:303:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m303\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    hit_events: &mut EventWriter<HitEvent>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_hit_events`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `time`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":4868,"byte_end":4872,"line_start":172,"line_end":172,"column_start":5,"column_end":9,"is_primary":true,"text":[{"text":"    time: Res<Time>,","highlight_start":5,"highlight_end":9}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":4868,"byte_end":4872,"line_start":172,"line_end":172,"column_start":5,"column_end":9,"is_primary":true,"text":[{"text":"    time: Res<Time>,","highlight_start":5,"highlight_end":9}],"label":null,"suggested_replacement":"_time","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `time`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/performance.rs:172:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m172\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    time: Res<Time>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_time`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `game_logic`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":8707,"byte_end":8717,"line_start":297,"line_end":297,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    game_logic: &mut ResMut<GameLogic>,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":8707,"byte_end":8717,"line_start":297,"line_end":297,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    game_logic: &mut ResMut<GameLogic>,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":"_game_logic","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `game_logic`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/performance.rs:297:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m297\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    game_logic: &mut ResMut<GameLogic>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_game_logic`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `special_move_events`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":8794,"byte_end":8813,"line_start":299,"line_end":299,"column_start":5,"column_end":24,"is_primary":true,"text":[{"text":"    special_move_events: &mut EventWriter<SpecialMoveEvent>,","highlight_start":5,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":8794,"byte_end":8813,"line_start":299,"line_end":299,"column_start":5,"column_end":24,"is_primary":true,"text":[{"text":"    special_move_events: &mut EventWriter<SpecialMoveEvent>,","highlight_start":5,"highlight_end":24}],"label":null,"suggested_replacement":"_special_move_events","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `special_move_events`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/performance.rs:299:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m299\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    special_move_events: &mut EventWriter<SpecialMoveEvent>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_special_move_events`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `hit_events`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":8855,"byte_end":8865,"line_start":300,"line_end":300,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    hit_events: &mut EventWriter<HitEvent>,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":8855,"byte_end":8865,"line_start":300,"line_end":300,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    hit_events: &mut EventWriter<HitEvent>,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":"_hit_events","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `hit_events`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/performance.rs:300:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m300\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    hit_events: &mut EventWriter<HitEvent>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_hit_events`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `physics_start`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":11215,"byte_end":11228,"line_start":361,"line_end":361,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    let physics_start = std::time::Instant::now();","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":11215,"byte_end":11228,"line_start":361,"line_end":361,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    let physics_start = std::time::Instant::now();","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":"_physics_start","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `physics_start`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/performance.rs:361:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m361\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let physics_start = std::time::Instant::now();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_physics_start`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `performance_config`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":11158,"byte_end":11176,"line_start":359,"line_end":359,"column_start":5,"column_end":23,"is_primary":true,"text":[{"text":"    performance_config: &Res<PerformanceConfig>,","highlight_start":5,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":11158,"byte_end":11176,"line_start":359,"line_end":359,"column_start":5,"column_end":23,"is_primary":true,"text":[{"text":"    performance_config: &Res<PerformanceConfig>,","highlight_start":5,"highlight_end":23}],"label":null,"suggested_replacement":"_performance_config","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `performance_config`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/performance.rs:359:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m359\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    performance_config: &Res<PerformanceConfig>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_performance_config`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `hit_events`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":13119,"byte_end":13129,"line_start":415,"line_end":415,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    hit_events: &mut EventWriter<HitEvent>,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":13119,"byte_end":13129,"line_start":415,"line_end":415,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    hit_events: &mut EventWriter<HitEvent>,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":"_hit_events","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `hit_events`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/performance.rs:415:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m415\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    hit_events: &mut EventWriter<HitEvent>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_hit_events`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/collision_system.rs","byte_start":2624,"byte_end":2644,"line_start":73,"line_end":73,"column_start":5,"column_end":25,"is_primary":true,"text":[{"text":"    mut collision_config: ResMut<CollisionSystemConfig>,","highlight_start":5,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/collision_system.rs","byte_start":2624,"byte_end":2628,"line_start":73,"line_end":73,"column_start":5,"column_end":9,"is_primary":true,"text":[{"text":"    mut collision_config: ResMut<CollisionSystemConfig>,","highlight_start":5,"highlight_end":9}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variable does not need to be mutable\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/collision_system.rs:73:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m73\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    mut collision_config: ResMut<CollisionSystemConfig>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mhelp: remove this `mut`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/collision_system.rs","byte_start":2841,"byte_end":2860,"line_start":77,"line_end":77,"column_start":5,"column_end":24,"is_primary":true,"text":[{"text":"    mut collision_query: CollisionQuery,","highlight_start":5,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/collision_system.rs","byte_start":2841,"byte_end":2845,"line_start":77,"line_end":77,"column_start":5,"column_end":9,"is_primary":true,"text":[{"text":"    mut collision_query: CollisionQuery,","highlight_start":5,"highlight_end":9}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variable does not need to be mutable\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/collision_system.rs:77:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m77\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    mut collision_query: CollisionQuery,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mhelp: remove this `mut`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `config`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/collision_system.rs","byte_start":5004,"byte_end":5010,"line_start":147,"line_end":147,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    config: &CollisionConfig,","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/collision_system.rs","byte_start":5004,"byte_end":5010,"line_start":147,"line_end":147,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    config: &CollisionConfig,","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":"_config","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `config`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/collision_system.rs:147:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m147\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    config: &CollisionConfig,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_config`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `collision_query`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/collision_system.rs","byte_start":11201,"byte_end":11216,"line_start":320,"line_end":320,"column_start":5,"column_end":20,"is_primary":true,"text":[{"text":"    collision_query: &CollisionQuery,","highlight_start":5,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/collision_system.rs","byte_start":11201,"byte_end":11216,"line_start":320,"line_end":320,"column_start":5,"column_end":20,"is_primary":true,"text":[{"text":"    collision_query: &CollisionQuery,","highlight_start":5,"highlight_end":20}],"label":null,"suggested_replacement":"_collision_query","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `collision_query`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/collision_system.rs:320:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m320\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    collision_query: &CollisionQuery,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_collision_query`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `config`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/collision_system.rs","byte_start":11239,"byte_end":11245,"line_start":321,"line_end":321,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    config: &CollisionConfig,","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/collision_system.rs","byte_start":11239,"byte_end":11245,"line_start":321,"line_end":321,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    config: &CollisionConfig,","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":"_config","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `config`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/collision_system.rs:321:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m321\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    config: &CollisionConfig,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_config`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `collision_query`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/collision_system.rs","byte_start":12092,"byte_end":12107,"line_start":348,"line_end":348,"column_start":5,"column_end":20,"is_primary":true,"text":[{"text":"    collision_query: &CollisionQuery,","highlight_start":5,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/collision_system.rs","byte_start":12092,"byte_end":12107,"line_start":348,"line_end":348,"column_start":5,"column_end":20,"is_primary":true,"text":[{"text":"    collision_query: &CollisionQuery,","highlight_start":5,"highlight_end":20}],"label":null,"suggested_replacement":"_collision_query","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `collision_query`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/collision_system.rs:348:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m348\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    collision_query: &CollisionQuery,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_collision_query`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `entity`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/character_system.rs","byte_start":5558,"byte_end":5564,"line_start":177,"line_end":177,"column_start":10,"column_end":16,"is_primary":true,"text":[{"text":"    for (entity, fighter, mut state, mut position, mut velocity, input) in fighter_query.iter_mut() {","highlight_start":10,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/character_system.rs","byte_start":5558,"byte_end":5564,"line_start":177,"line_end":177,"column_start":10,"column_end":16,"is_primary":true,"text":[{"text":"    for (entity, fighter, mut state, mut position, mut velocity, input) in fighter_query.iter_mut() {","highlight_start":10,"highlight_end":16}],"label":null,"suggested_replacement":"_entity","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `entity`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/character_system.rs:177:10\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m177\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    for (entity, fighter, mut state, mut position, mut velocity, input) in fighter_query.iter_mut() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_entity`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `anim_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/character_system.rs","byte_start":6546,"byte_end":6553,"line_start":193,"line_end":193,"column_start":49,"column_end":56,"is_primary":true,"text":[{"text":"                    FighterAction::SetAnimation(anim_id) => {","highlight_start":49,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/character_system.rs","byte_start":6546,"byte_end":6553,"line_start":193,"line_end":193,"column_start":49,"column_end":56,"is_primary":true,"text":[{"text":"                    FighterAction::SetAnimation(anim_id) => {","highlight_start":49,"highlight_end":56}],"label":null,"suggested_replacement":"_anim_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `anim_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/character_system.rs:193:49\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m193\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    FighterAction::SetAnimation(anim_id) => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_anim_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/character_system.rs","byte_start":8107,"byte_end":8112,"line_start":231,"line_end":231,"column_start":23,"column_end":28,"is_primary":true,"text":[{"text":"    for (fighter, mut state) in fighter_query.iter_mut() {","highlight_start":23,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/character_system.rs","byte_start":8107,"byte_end":8112,"line_start":231,"line_end":231,"column_start":23,"column_end":28,"is_primary":true,"text":[{"text":"    for (fighter, mut state) in fighter_query.iter_mut() {","highlight_start":23,"highlight_end":28}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/character_system.rs:231:23\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m231\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    for (fighter, mut state) in fighter_query.iter_mut() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                       \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/character_system.rs","byte_start":8103,"byte_end":8112,"line_start":231,"line_end":231,"column_start":19,"column_end":28,"is_primary":true,"text":[{"text":"    for (fighter, mut state) in fighter_query.iter_mut() {","highlight_start":19,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/character_system.rs","byte_start":8103,"byte_end":8107,"line_start":231,"line_end":231,"column_start":19,"column_end":23,"is_primary":true,"text":[{"text":"    for (fighter, mut state) in fighter_query.iter_mut() {","highlight_start":19,"highlight_end":23}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variable does not need to be mutable\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/character_system.rs:231:19\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m231\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    for (fighter, mut state) in fighter_query.iter_mut() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mhelp: remove this `mut`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":6528,"byte_end":6533,"line_start":190,"line_end":190,"column_start":28,"column_end":33,"is_primary":true,"text":[{"text":"    fn get_hitboxes(&self, state: &FighterStateData) -> Vec<HitboxData> {","highlight_start":28,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":6528,"byte_end":6533,"line_start":190,"line_end":190,"column_start":28,"column_end":33,"is_primary":true,"text":[{"text":"    fn get_hitboxes(&self, state: &FighterStateData) -> Vec<HitboxData> {","highlight_start":28,"highlight_end":33}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ryu.rs:190:28\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m190\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn get_hitboxes(&self, state: &FighterStateData) -> Vec<HitboxData> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":6770,"byte_end":6775,"line_start":196,"line_end":196,"column_start":29,"column_end":34,"is_primary":true,"text":[{"text":"    fn get_hurtboxes(&self, state: &FighterStateData) -> Vec<HurtboxData> {","highlight_start":29,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":6770,"byte_end":6775,"line_start":196,"line_end":196,"column_start":29,"column_end":34,"is_primary":true,"text":[{"text":"    fn get_hurtboxes(&self, state: &FighterStateData) -> Vec<HurtboxData> {","highlight_start":29,"highlight_end":34}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ryu.rs:196:29\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m196\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn get_hurtboxes(&self, state: &FighterStateData) -> Vec<HurtboxData> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":7138,"byte_end":7143,"line_start":207,"line_end":207,"column_start":43,"column_end":48,"is_primary":true,"text":[{"text":"    fn on_hit(&self, attack: &AttackData, state: &mut FighterStateData) -> HitResponse {","highlight_start":43,"highlight_end":48}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":7138,"byte_end":7143,"line_start":207,"line_end":207,"column_start":43,"column_end":48,"is_primary":true,"text":[{"text":"    fn on_hit(&self, attack: &AttackData, state: &mut FighterStateData) -> HitResponse {","highlight_start":43,"highlight_end":48}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ryu.rs:207:43\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m207\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn on_hit(&self, attack: &AttackData, state: &mut FighterStateData) -> HitResponse {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":7573,"byte_end":7578,"line_start":218,"line_end":218,"column_start":45,"column_end":50,"is_primary":true,"text":[{"text":"    fn on_block(&self, attack: &AttackData, state: &mut FighterStateData) -> BlockResponse {","highlight_start":45,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":7573,"byte_end":7578,"line_start":218,"line_end":218,"column_start":45,"column_end":50,"is_primary":true,"text":[{"text":"    fn on_block(&self, attack: &AttackData, state: &mut FighterStateData) -> BlockResponse {","highlight_start":45,"highlight_end":50}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ryu.rs:218:45\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m218\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn on_block(&self, attack: &AttackData, state: &mut FighterStateData) -> BlockResponse {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":9164,"byte_end":9169,"line_start":262,"line_end":262,"column_start":55,"column_end":60,"is_primary":true,"text":[{"text":"    fn check_special_moves(&self, input: &InputState, state: &FighterStateData) -> Option<FighterAction> {","highlight_start":55,"highlight_end":60}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":9164,"byte_end":9169,"line_start":262,"line_end":262,"column_start":55,"column_end":60,"is_primary":true,"text":[{"text":"    fn check_special_moves(&self, input: &InputState, state: &FighterStateData) -> Option<FighterAction> {","highlight_start":55,"highlight_end":60}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ryu.rs:262:55\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m262\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn check_special_moves(&self, input: &InputState, state: &FighterStateData) -> Option<FighterAction> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                       \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":10596,"byte_end":10601,"line_start":300,"line_end":300,"column_start":31,"column_end":36,"is_primary":true,"text":[{"text":"    fn execute_hadoken(&self, state: &mut FighterStateData) -> SpecialMoveResult {","highlight_start":31,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":10596,"byte_end":10601,"line_start":300,"line_end":300,"column_start":31,"column_end":36,"is_primary":true,"text":[{"text":"    fn execute_hadoken(&self, state: &mut FighterStateData) -> SpecialMoveResult {","highlight_start":31,"highlight_end":36}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ryu.rs:300:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m300\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn execute_hadoken(&self, state: &mut FighterStateData) -> SpecialMoveResult {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":11036,"byte_end":11041,"line_start":313,"line_end":313,"column_start":33,"column_end":38,"is_primary":true,"text":[{"text":"    fn execute_shoryuken(&self, state: &mut FighterStateData) -> SpecialMoveResult {","highlight_start":33,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":11036,"byte_end":11041,"line_start":313,"line_end":313,"column_start":33,"column_end":38,"is_primary":true,"text":[{"text":"    fn execute_shoryuken(&self, state: &mut FighterStateData) -> SpecialMoveResult {","highlight_start":33,"highlight_end":38}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ryu.rs:313:33\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m313\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn execute_shoryuken(&self, state: &mut FighterStateData) -> SpecialMoveResult {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":11574,"byte_end":11579,"line_start":327,"line_end":327,"column_start":33,"column_end":38,"is_primary":true,"text":[{"text":"    fn execute_tatsumaki(&self, state: &mut FighterStateData) -> SpecialMoveResult {","highlight_start":33,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":11574,"byte_end":11579,"line_start":327,"line_end":327,"column_start":33,"column_end":38,"is_primary":true,"text":[{"text":"    fn execute_tatsumaki(&self, state: &mut FighterStateData) -> SpecialMoveResult {","highlight_start":33,"highlight_end":38}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ryu.rs:327:33\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m327\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn execute_tatsumaki(&self, state: &mut FighterStateData) -> SpecialMoveResult {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":6726,"byte_end":6731,"line_start":190,"line_end":190,"column_start":28,"column_end":33,"is_primary":true,"text":[{"text":"    fn get_hitboxes(&self, state: &FighterStateData) -> Vec<HitboxData> {","highlight_start":28,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":6726,"byte_end":6731,"line_start":190,"line_end":190,"column_start":28,"column_end":33,"is_primary":true,"text":[{"text":"    fn get_hitboxes(&self, state: &FighterStateData) -> Vec<HitboxData> {","highlight_start":28,"highlight_end":33}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ken.rs:190:28\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m190\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn get_hitboxes(&self, state: &FighterStateData) -> Vec<HitboxData> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":6827,"byte_end":6832,"line_start":194,"line_end":194,"column_start":29,"column_end":34,"is_primary":true,"text":[{"text":"    fn get_hurtboxes(&self, state: &FighterStateData) -> Vec<HurtboxData> {","highlight_start":29,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":6827,"byte_end":6832,"line_start":194,"line_end":194,"column_start":29,"column_end":34,"is_primary":true,"text":[{"text":"    fn get_hurtboxes(&self, state: &FighterStateData) -> Vec<HurtboxData> {","highlight_start":29,"highlight_end":34}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ken.rs:194:29\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m194\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn get_hurtboxes(&self, state: &FighterStateData) -> Vec<HurtboxData> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":7134,"byte_end":7139,"line_start":204,"line_end":204,"column_start":43,"column_end":48,"is_primary":true,"text":[{"text":"    fn on_hit(&self, attack: &AttackData, state: &mut FighterStateData) -> HitResponse {","highlight_start":43,"highlight_end":48}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":7134,"byte_end":7139,"line_start":204,"line_end":204,"column_start":43,"column_end":48,"is_primary":true,"text":[{"text":"    fn on_hit(&self, attack: &AttackData, state: &mut FighterStateData) -> HitResponse {","highlight_start":43,"highlight_end":48}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ken.rs:204:43\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m204\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn on_hit(&self, attack: &AttackData, state: &mut FighterStateData) -> HitResponse {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":7544,"byte_end":7549,"line_start":215,"line_end":215,"column_start":45,"column_end":50,"is_primary":true,"text":[{"text":"    fn on_block(&self, attack: &AttackData, state: &mut FighterStateData) -> BlockResponse {","highlight_start":45,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":7544,"byte_end":7549,"line_start":215,"line_end":215,"column_start":45,"column_end":50,"is_primary":true,"text":[{"text":"    fn on_block(&self, attack: &AttackData, state: &mut FighterStateData) -> BlockResponse {","highlight_start":45,"highlight_end":50}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ken.rs:215:45\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m215\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn on_block(&self, attack: &AttackData, state: &mut FighterStateData) -> BlockResponse {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":9288,"byte_end":9293,"line_start":259,"line_end":259,"column_start":55,"column_end":60,"is_primary":true,"text":[{"text":"    fn check_special_moves(&self, input: &InputState, state: &FighterStateData) -> Option<FighterAction> {","highlight_start":55,"highlight_end":60}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":9288,"byte_end":9293,"line_start":259,"line_end":259,"column_start":55,"column_end":60,"is_primary":true,"text":[{"text":"    fn check_special_moves(&self, input: &InputState, state: &FighterStateData) -> Option<FighterAction> {","highlight_start":55,"highlight_end":60}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ken.rs:259:55\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m259\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn check_special_moves(&self, input: &InputState, state: &FighterStateData) -> Option<FighterAction> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                       \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":10304,"byte_end":10309,"line_start":289,"line_end":289,"column_start":31,"column_end":36,"is_primary":true,"text":[{"text":"    fn execute_hadoken(&self, state: &mut FighterStateData) -> SpecialMoveResult {","highlight_start":31,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":10304,"byte_end":10309,"line_start":289,"line_end":289,"column_start":31,"column_end":36,"is_primary":true,"text":[{"text":"    fn execute_hadoken(&self, state: &mut FighterStateData) -> SpecialMoveResult {","highlight_start":31,"highlight_end":36}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ken.rs:289:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m289\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn execute_hadoken(&self, state: &mut FighterStateData) -> SpecialMoveResult {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":10767,"byte_end":10772,"line_start":302,"line_end":302,"column_start":33,"column_end":38,"is_primary":true,"text":[{"text":"    fn execute_shoryuken(&self, state: &mut FighterStateData) -> SpecialMoveResult {","highlight_start":33,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":10767,"byte_end":10772,"line_start":302,"line_end":302,"column_start":33,"column_end":38,"is_primary":true,"text":[{"text":"    fn execute_shoryuken(&self, state: &mut FighterStateData) -> SpecialMoveResult {","highlight_start":33,"highlight_end":38}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ken.rs:302:33\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m302\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn execute_shoryuken(&self, state: &mut FighterStateData) -> SpecialMoveResult {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":11398,"byte_end":11403,"line_start":317,"line_end":317,"column_start":33,"column_end":38,"is_primary":true,"text":[{"text":"    fn execute_tatsumaki(&self, state: &mut FighterStateData) -> SpecialMoveResult {","highlight_start":33,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":11398,"byte_end":11403,"line_start":317,"line_end":317,"column_start":33,"column_end":38,"is_primary":true,"text":[{"text":"    fn execute_tatsumaki(&self, state: &mut FighterStateData) -> SpecialMoveResult {","highlight_start":33,"highlight_end":38}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ken.rs:317:33\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m317\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn execute_tatsumaki(&self, state: &mut FighterStateData) -> SpecialMoveResult {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/blanka.rs","byte_start":1690,"byte_end":1695,"line_start":58,"line_end":58,"column_start":49,"column_end":54,"is_primary":true,"text":[{"text":"    fn process_input(&self, input: &InputState, state: &mut FighterStateData) -> Vec<FighterAction> {","highlight_start":49,"highlight_end":54}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/blanka.rs","byte_start":1690,"byte_end":1695,"line_start":58,"line_end":58,"column_start":49,"column_end":54,"is_primary":true,"text":[{"text":"    fn process_input(&self, input: &InputState, state: &mut FighterStateData) -> Vec<FighterAction> {","highlight_start":49,"highlight_end":54}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/blanka.rs:58:49\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m58\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn process_input(&self, input: &InputState, state: &mut FighterStateData) -> Vec<FighterAction> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `fighter_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_core.rs","byte_start":6369,"byte_end":6379,"line_start":199,"line_end":199,"column_start":35,"column_end":45,"is_primary":true,"text":[{"text":"    pub fn init_player(&mut self, fighter_id: FighterId, difficulty: u8) {","highlight_start":35,"highlight_end":45}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_core.rs","byte_start":6369,"byte_end":6379,"line_start":199,"line_end":199,"column_start":35,"column_end":45,"is_primary":true,"text":[{"text":"    pub fn init_player(&mut self, fighter_id: FighterId, difficulty: u8) {","highlight_start":35,"highlight_end":45}],"label":null,"suggested_replacement":"_fighter_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `fighter_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_core.rs:199:35\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m199\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn init_player(&mut self, fighter_id: FighterId, difficulty: u8) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_fighter_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `difficulty`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_core.rs","byte_start":6392,"byte_end":6402,"line_start":199,"line_end":199,"column_start":58,"column_end":68,"is_primary":true,"text":[{"text":"    pub fn init_player(&mut self, fighter_id: FighterId, difficulty: u8) {","highlight_start":58,"highlight_end":68}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_core.rs","byte_start":6392,"byte_end":6402,"line_start":199,"line_end":199,"column_start":58,"column_end":68,"is_primary":true,"text":[{"text":"    pub fn init_player(&mut self, fighter_id: FighterId, difficulty: u8) {","highlight_start":58,"highlight_end":68}],"label":null,"suggested_replacement":"_difficulty","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `difficulty`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_core.rs:199:58\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m199\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn init_player(&mut self, fighter_id: FighterId, difficulty: u8) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                          \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_difficulty`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `opponent_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_strategy.rs","byte_start":3345,"byte_end":3356,"line_start":111,"line_end":111,"column_start":74,"column_end":85,"is_primary":true,"text":[{"text":"    pub fn load_character_strategies(&mut self, character_id: FighterId, opponent_id: FighterId) {","highlight_start":74,"highlight_end":85}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_strategy.rs","byte_start":3345,"byte_end":3356,"line_start":111,"line_end":111,"column_start":74,"column_end":85,"is_primary":true,"text":[{"text":"    pub fn load_character_strategies(&mut self, character_id: FighterId, opponent_id: FighterId) {","highlight_start":74,"highlight_end":85}],"label":null,"suggested_replacement":"_opponent_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `opponent_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_strategy.rs:111:74\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m111\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn load_character_strategies(&mut self, character_id: FighterId, opponent_id: FighterId) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                          \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_opponent_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `fighter_query`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_difficulty.rs","byte_start":17330,"byte_end":17343,"line_start":434,"line_end":434,"column_start":5,"column_end":18,"is_primary":true,"text":[{"text":"    fighter_query: Query<&FighterStateData, With<Fighter>>,","highlight_start":5,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_difficulty.rs","byte_start":17330,"byte_end":17343,"line_start":434,"line_end":434,"column_start":5,"column_end":18,"is_primary":true,"text":[{"text":"    fighter_query: Query<&FighterStateData, With<Fighter>>,","highlight_start":5,"highlight_end":18}],"label":null,"suggested_replacement":"_fighter_query","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `fighter_query`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_difficulty.rs:434:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m434\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fighter_query: Query<&FighterStateData, With<Fighter>>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_fighter_query`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `performance_tracker`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_difficulty.rs","byte_start":17415,"byte_end":17434,"line_start":436,"line_end":436,"column_start":9,"column_end":28,"is_primary":true,"text":[{"text":"    mut performance_tracker: Local<HashMap<Entity, AIPerformanceMetrics>>,","highlight_start":9,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_difficulty.rs","byte_start":17415,"byte_end":17434,"line_start":436,"line_end":436,"column_start":9,"column_end":28,"is_primary":true,"text":[{"text":"    mut performance_tracker: Local<HashMap<Entity, AIPerformanceMetrics>>,","highlight_start":9,"highlight_end":28}],"label":null,"suggested_replacement":"_performance_tracker","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `performance_tracker`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_difficulty.rs:436:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m436\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    mut performance_tracker: Local<HashMap<Entity, AIPerformanceMetrics>>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_performance_tracker`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_difficulty.rs","byte_start":17411,"byte_end":17434,"line_start":436,"line_end":436,"column_start":5,"column_end":28,"is_primary":true,"text":[{"text":"    mut performance_tracker: Local<HashMap<Entity, AIPerformanceMetrics>>,","highlight_start":5,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_difficulty.rs","byte_start":17411,"byte_end":17415,"line_start":436,"line_end":436,"column_start":5,"column_end":9,"is_primary":true,"text":[{"text":"    mut performance_tracker: Local<HashMap<Entity, AIPerformanceMetrics>>,","highlight_start":5,"highlight_end":9}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variable does not need to be mutable\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_difficulty.rs:436:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m436\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    mut performance_tracker: Local<HashMap<Entity, AIPerformanceMetrics>>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mhelp: remove this `mut`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `fighter_query`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_difficulty.rs","byte_start":17934,"byte_end":17947,"line_start":450,"line_end":450,"column_start":5,"column_end":18,"is_primary":true,"text":[{"text":"    fighter_query: Query<&FighterStateData, With<Fighter>>,","highlight_start":5,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_difficulty.rs","byte_start":17934,"byte_end":17947,"line_start":450,"line_end":450,"column_start":5,"column_end":18,"is_primary":true,"text":[{"text":"    fighter_query: Query<&FighterStateData, With<Fighter>>,","highlight_start":5,"highlight_end":18}],"label":null,"suggested_replacement":"_fighter_query","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `fighter_query`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_difficulty.rs:450:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m450\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fighter_query: Query<&FighterStateData, With<Fighter>>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_fighter_query`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `frame_count`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_threat.rs","byte_start":24439,"byte_end":24450,"line_start":752,"line_end":752,"column_start":84,"column_end":95,"is_primary":true,"text":[{"text":"    fn update_opponent_tracking(&mut self, threat_assessment: &AIThreatAssessment, frame_count: u64) {","highlight_start":84,"highlight_end":95}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_threat.rs","byte_start":24439,"byte_end":24450,"line_start":752,"line_end":752,"column_start":84,"column_end":95,"is_primary":true,"text":[{"text":"    fn update_opponent_tracking(&mut self, threat_assessment: &AIThreatAssessment, frame_count: u64) {","highlight_start":84,"highlight_end":95}],"label":null,"suggested_replacement":"_frame_count","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `frame_count`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_threat.rs:752:84\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m752\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn update_opponent_tracking(&mut self, threat_assessment: &AIThreatAssessment, frame_count: u64) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                                    \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_frame_count`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `decision`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_threat.rs","byte_start":47144,"byte_end":47152,"line_start":1337,"line_end":1337,"column_start":21,"column_end":29,"is_primary":true,"text":[{"text":"        if let Some(decision) = decision_maker.make_decision(","highlight_start":21,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_threat.rs","byte_start":47144,"byte_end":47152,"line_start":1337,"line_end":1337,"column_start":21,"column_end":29,"is_primary":true,"text":[{"text":"        if let Some(decision) = decision_maker.make_decision(","highlight_start":21,"highlight_end":29}],"label":null,"suggested_replacement":"_decision","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `decision`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_threat.rs:1337:21\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1337\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        if let Some(decision) = decision_maker.make_decision(\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_decision`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `frame`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_debug.rs","byte_start":8532,"byte_end":8537,"line_start":301,"line_end":301,"column_start":61,"column_end":66,"is_primary":true,"text":[{"text":"    pub fn record_decision(&mut self, decision: AIDecision, frame: u64) {","highlight_start":61,"highlight_end":66}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_debug.rs","byte_start":8532,"byte_end":8537,"line_start":301,"line_end":301,"column_start":61,"column_end":66,"is_primary":true,"text":[{"text":"    pub fn record_decision(&mut self, decision: AIDecision, frame: u64) {","highlight_start":61,"highlight_end":66}],"label":null,"suggested_replacement":"_frame","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `frame`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_debug.rs:301:61\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m301\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn record_decision(&mut self, decision: AIDecision, frame: u64) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_frame`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `round_duration_frames`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_debug.rs","byte_start":11283,"byte_end":11304,"line_start":384,"line_end":384,"column_start":56,"column_end":77,"is_primary":true,"text":[{"text":"    pub fn update_win_loss_stats(&mut self, won: bool, round_duration_frames: u32) {","highlight_start":56,"highlight_end":77}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_debug.rs","byte_start":11283,"byte_end":11304,"line_start":384,"line_end":384,"column_start":56,"column_end":77,"is_primary":true,"text":[{"text":"    pub fn update_win_loss_stats(&mut self, won: bool, round_duration_frames: u32) {","highlight_start":56,"highlight_end":77}],"label":null,"suggested_replacement":"_round_duration_frames","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `round_duration_frames`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_debug.rs:384:56\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m384\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn update_win_loss_stats(&mut self, won: bool, round_duration_frames: u32) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_round_duration_frames`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `entity`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_debug.rs","byte_start":25168,"byte_end":25174,"line_start":780,"line_end":780,"column_start":10,"column_end":16,"is_primary":true,"text":[{"text":"    for (entity, mut debug_info, ai_core, difficulty, threat_assessment, decision_maker) in debug_query.iter_mut() {","highlight_start":10,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_debug.rs","byte_start":25168,"byte_end":25174,"line_start":780,"line_end":780,"column_start":10,"column_end":16,"is_primary":true,"text":[{"text":"    for (entity, mut debug_info, ai_core, difficulty, threat_assessment, decision_maker) in debug_query.iter_mut() {","highlight_start":10,"highlight_end":16}],"label":null,"suggested_replacement":"_entity","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `entity`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_debug.rs:780:10\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m780\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    for (entity, mut debug_info, ai_core, difficulty, threat_assessment, decision_maker) in debug_query.iter_mut() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_entity`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `difficulty`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_debug.rs","byte_start":25201,"byte_end":25211,"line_start":780,"line_end":780,"column_start":43,"column_end":53,"is_primary":true,"text":[{"text":"    for (entity, mut debug_info, ai_core, difficulty, threat_assessment, decision_maker) in debug_query.iter_mut() {","highlight_start":43,"highlight_end":53}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_debug.rs","byte_start":25201,"byte_end":25211,"line_start":780,"line_end":780,"column_start":43,"column_end":53,"is_primary":true,"text":[{"text":"    for (entity, mut debug_info, ai_core, difficulty, threat_assessment, decision_maker) in debug_query.iter_mut() {","highlight_start":43,"highlight_end":53}],"label":null,"suggested_replacement":"_difficulty","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `difficulty`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_debug.rs:780:43\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m780\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    for (entity, mut debug_info, ai_core, difficulty, threat_assessment, decision_maker) in debug_query.iter_mut() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_difficulty`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `p`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_character_patterns.rs","byte_start":24342,"byte_end":24343,"line_start":621,"line_end":621,"column_start":22,"column_end":23,"is_primary":true,"text":[{"text":"            .filter(|p| difficulty >= 3) // Only use counters at higher difficulty","highlight_start":22,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_character_patterns.rs","byte_start":24342,"byte_end":24343,"line_start":621,"line_end":621,"column_start":22,"column_end":23,"is_primary":true,"text":[{"text":"            .filter(|p| difficulty >= 3) // Only use counters at higher difficulty","highlight_start":22,"highlight_end":23}],"label":null,"suggested_replacement":"_p","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `p`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_character_patterns.rs:621:22\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m621\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .filter(|p| difficulty >= 3) // Only use counters at higher difficulty\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_p`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `current_frame`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_character_patterns.rs","byte_start":28771,"byte_end":28784,"line_start":728,"line_end":728,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    let current_frame = (time.elapsed_secs() * 60.0) as u64; // Assuming 60 FPS","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_character_patterns.rs","byte_start":28771,"byte_end":28784,"line_start":728,"line_end":728,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    let current_frame = (time.elapsed_secs() * 60.0) as u64; // Assuming 60 FPS","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":"_current_frame","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `current_frame`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_character_patterns.rs:728:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m728\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let current_frame = (time.elapsed_secs() * 60.0) as u64; // Assuming 60 FPS\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_current_frame`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable `failure_count` is assigned to, but never used","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_behavior_tree.rs","byte_start":23184,"byte_end":23197,"line_start":722,"line_end":722,"column_start":17,"column_end":30,"is_primary":true,"text":[{"text":"        let mut failure_count = 0;","highlight_start":17,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider using `_failure_count` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variable `failure_count` is assigned to, but never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_behavior_tree.rs:722:17\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m722\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut failure_count = 0;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: consider using `_failure_count` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `attack_type`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_behavior_tree.rs","byte_start":27738,"byte_end":27749,"line_start":846,"line_end":846,"column_start":30,"column_end":41,"is_primary":true,"text":[{"text":"            AIAction::Attack(attack_type) => {","highlight_start":30,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_behavior_tree.rs","byte_start":27738,"byte_end":27749,"line_start":846,"line_end":846,"column_start":30,"column_end":41,"is_primary":true,"text":[{"text":"            AIAction::Attack(attack_type) => {","highlight_start":30,"highlight_end":41}],"label":null,"suggested_replacement":"_attack_type","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `attack_type`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_behavior_tree.rs:846:30\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m846\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            AIAction::Attack(attack_type) => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_attack_type`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `special_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_behavior_tree.rs","byte_start":27953,"byte_end":27963,"line_start":851,"line_end":851,"column_start":35,"column_end":45,"is_primary":true,"text":[{"text":"            AIAction::SpecialMove(special_id) => {","highlight_start":35,"highlight_end":45}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_behavior_tree.rs","byte_start":27953,"byte_end":27963,"line_start":851,"line_end":851,"column_start":35,"column_end":45,"is_primary":true,"text":[{"text":"            AIAction::SpecialMove(special_id) => {","highlight_start":35,"highlight_end":45}],"label":null,"suggested_replacement":"_special_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `special_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_behavior_tree.rs:851:35\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m851\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            AIAction::SpecialMove(special_id) => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_special_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `context`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_behavior_tree.rs","byte_start":29773,"byte_end":29780,"line_start":900,"line_end":900,"column_start":9,"column_end":16,"is_primary":true,"text":[{"text":"        context: &mut BehaviorExecutionContext,","highlight_start":9,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_behavior_tree.rs","byte_start":29773,"byte_end":29780,"line_start":900,"line_end":900,"column_start":9,"column_end":16,"is_primary":true,"text":[{"text":"        context: &mut BehaviorExecutionContext,","highlight_start":9,"highlight_end":16}],"label":null,"suggested_replacement":"_context","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `context`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_behavior_tree.rs:900:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m900\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        context: &mut BehaviorExecutionContext,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_context`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `context`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_behavior_tree.rs","byte_start":31118,"byte_end":31125,"line_start":939,"line_end":939,"column_start":9,"column_end":16,"is_primary":true,"text":[{"text":"        context: &mut BehaviorExecutionContext,","highlight_start":9,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_behavior_tree.rs","byte_start":31118,"byte_end":31125,"line_start":939,"line_end":939,"column_start":9,"column_end":16,"is_primary":true,"text":[{"text":"        context: &mut BehaviorExecutionContext,","highlight_start":9,"highlight_end":16}],"label":null,"suggested_replacement":"_context","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `context`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_behavior_tree.rs:939:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m939\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        context: &mut BehaviorExecutionContext,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_context`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `ai_core`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_behavior_tree.rs","byte_start":32444,"byte_end":32451,"line_start":971,"line_end":971,"column_start":40,"column_end":47,"is_primary":true,"text":[{"text":"    fn has_projectile_advantage(&self, ai_core: &AICore, context: &BehaviorExecutionContext) -> bool {","highlight_start":40,"highlight_end":47}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_behavior_tree.rs","byte_start":32444,"byte_end":32451,"line_start":971,"line_end":971,"column_start":40,"column_end":47,"is_primary":true,"text":[{"text":"    fn has_projectile_advantage(&self, ai_core: &AICore, context: &BehaviorExecutionContext) -> bool {","highlight_start":40,"highlight_end":47}],"label":null,"suggested_replacement":"_ai_core","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `ai_core`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_behavior_tree.rs:971:40\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m971\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn has_projectile_advantage(&self, ai_core: &AICore, context: &BehaviorExecutionContext) -> bool {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_ai_core`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `ai_core`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_behavior_tree.rs","byte_start":32762,"byte_end":32769,"line_start":978,"line_end":978,"column_start":27,"column_end":34,"is_primary":true,"text":[{"text":"    fn is_cornered(&self, ai_core: &AICore, context: &BehaviorExecutionContext) -> bool {","highlight_start":27,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_behavior_tree.rs","byte_start":32762,"byte_end":32769,"line_start":978,"line_end":978,"column_start":27,"column_end":34,"is_primary":true,"text":[{"text":"    fn is_cornered(&self, ai_core: &AICore, context: &BehaviorExecutionContext) -> bool {","highlight_start":27,"highlight_end":34}],"label":null,"suggested_replacement":"_ai_core","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `ai_core`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_behavior_tree.rs:978:27\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m978\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn is_cornered(&self, ai_core: &AICore, context: &BehaviorExecutionContext) -> bool {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_ai_core`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `ai_core`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_behavior_tree.rs","byte_start":33360,"byte_end":33367,"line_start":990,"line_end":990,"column_start":28,"column_end":35,"is_primary":true,"text":[{"text":"    fn should_throw(&self, ai_core: &AICore, context: &BehaviorExecutionContext) -> bool {","highlight_start":28,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_behavior_tree.rs","byte_start":33360,"byte_end":33367,"line_start":990,"line_end":990,"column_start":28,"column_end":35,"is_primary":true,"text":[{"text":"    fn should_throw(&self, ai_core: &AICore, context: &BehaviorExecutionContext) -> bool {","highlight_start":28,"highlight_end":35}],"label":null,"suggested_replacement":"_ai_core","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `ai_core`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_behavior_tree.rs:990:28\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m990\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn should_throw(&self, ai_core: &AICore, context: &BehaviorExecutionContext) -> bool {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_ai_core`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `time`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_behavior_tree.rs","byte_start":33896,"byte_end":33900,"line_start":1007,"line_end":1007,"column_start":5,"column_end":9,"is_primary":true,"text":[{"text":"    time: Res<Time>,","highlight_start":5,"highlight_end":9}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_behavior_tree.rs","byte_start":33896,"byte_end":33900,"line_start":1007,"line_end":1007,"column_start":5,"column_end":9,"is_primary":true,"text":[{"text":"    time: Res<Time>,","highlight_start":5,"highlight_end":9}],"label":null,"suggested_replacement":"_time","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `time`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_behavior_tree.rs:1007:5\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1007\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    time: Res<Time>,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_time`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"fields `fighter_extended` and `animations` are never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":2311,"byte_end":2332,"line_start":85,"line_end":85,"column_start":12,"column_end":33,"is_primary":false,"text":[{"text":"pub struct OptimizedFighterQuery<'w, 's> {","highlight_start":12,"highlight_end":33}],"label":"fields in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/performance.rs","byte_start":2601,"byte_end":2617,"line_start":95,"line_end":95,"column_start":5,"column_end":21,"is_primary":true,"text":[{"text":"    fighter_extended: Query<'w, 's, (","highlight_start":5,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/performance.rs","byte_start":2821,"byte_end":2831,"line_start":102,"line_end":102,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    animations: Query<'w, 's, &'static mut AnimationState, With<Fighter>>,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: fields `fighter_extended` and `animations` are never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/performance.rs:95:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m85\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct OptimizedFighterQuery<'w, 's> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfields in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m95\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fighter_extended: Query<'w, 's, (\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m102\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    animations: Query<'w, 's, &'static mut AnimationState, With<Fighter>>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"methods `get_adjusted_pattern`, `record_pattern_performance`, `get_pattern_success_rate`, `select_attack_pattern`, and `select_defensive_action` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_character_patterns.rs","byte_start":10689,"byte_end":10719,"line_start":313,"line_end":313,"column_start":1,"column_end":31,"is_primary":false,"text":[{"text":"impl CharacterAIPatternManager {","highlight_start":1,"highlight_end":31}],"label":"methods in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/ai/ai_character_patterns.rs","byte_start":20745,"byte_end":20765,"line_start":544,"line_end":544,"column_start":8,"column_end":28,"is_primary":true,"text":[{"text":"    fn get_adjusted_pattern(&self, base_pattern: &AIBehaviorPattern) -> AIBehaviorPattern {","highlight_start":8,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/ai/ai_character_patterns.rs","byte_start":21741,"byte_end":21767,"line_start":560,"line_end":560,"column_start":8,"column_end":34,"is_primary":true,"text":[{"text":"    fn record_pattern_performance(&mut self, pattern_name: &str, success: bool, current_frame: u64) {","highlight_start":8,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/ai/ai_character_patterns.rs","byte_start":22394,"byte_end":22418,"line_start":574,"line_end":574,"column_start":8,"column_end":32,"is_primary":true,"text":[{"text":"    fn get_pattern_success_rate(&self, pattern_name: &str) -> f32 {","highlight_start":8,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/ai/ai_character_patterns.rs","byte_start":22636,"byte_end":22657,"line_start":579,"line_end":579,"column_start":8,"column_end":29,"is_primary":true,"text":[{"text":"    fn select_attack_pattern(&self, distance: u16, difficulty: u8, frame_advantage: i8) -> Option<AIAttackType> {","highlight_start":8,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/ai/ai_character_patterns.rs","byte_start":23806,"byte_end":23829,"line_start":611,"line_end":611,"column_start":8,"column_end":31,"is_primary":true,"text":[{"text":"    fn select_defensive_action(&self, incoming_attack: AIAttackType, difficulty: u8) -> Option<AIAction> {","highlight_start":8,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: methods `get_adjusted_pattern`, `record_pattern_performance`, `get_pattern_success_rate`, `select_attack_pattern`, and `select_defensive_action` are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_character_patterns.rs:544:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m313\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl CharacterAIPatternManager {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mmethods in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m544\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn get_adjusted_pattern(&self, base_pattern: &AIBehaviorPattern) -> AIBehaviorPattern {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m560\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn record_pattern_performance(&mut self, pattern_name: &str, success: bool, current_frame: u64) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m574\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn get_pattern_success_rate(&self, pattern_name: &str) -> f32 {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m579\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn select_attack_pattern(&self, distance: u16, difficulty: u8, frame_advantage: i8) -> Option<AIAttackType> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m611\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn select_defensive_action(&self, incoming_attack: AIAttackType, difficulty: u8) -> Option<AIAction> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"92 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 92 warnings emitted\u001b[0m\n\n"}
