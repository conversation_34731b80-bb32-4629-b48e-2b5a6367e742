{"$message_type":"diagnostic","message":"unused import: `sf2_types::GameState as SF2GameState`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/qa_testing.rs","byte_start":468,"byte_end":504,"line_start":15,"line_end":15,"column_start":5,"column_end":41,"is_primary":true,"text":[{"text":"use sf2_types::GameState as SF2GameState;","highlight_start":5,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/qa_testing.rs","byte_start":464,"byte_end":506,"line_start":15,"line_end":16,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use sf2_types::GameState as SF2GameState;","highlight_start":1,"highlight_end":42},{"text":"use sf2_assets::{RomData, AssetExtractionProgress, ExtractedSprites, ExtractedAudio};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `sf2_types::GameState as SF2GameState`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/qa_testing.rs:15:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse sf2_types::GameState as SF2GameState;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `direction` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/systems.rs","byte_start":846,"byte_end":855,"line_start":27,"line_end":27,"column_start":17,"column_end":26,"is_primary":true,"text":[{"text":"        let mut direction = InputDirection::Neutral;","highlight_start":17,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`#[warn(unused_assignments)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: value assigned to `direction` is never read\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/systems.rs:27:17\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut direction = InputDirection::Neutral;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_assignments)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `buffer_query`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/systems.rs","byte_start":466,"byte_end":478,"line_start":17,"line_end":17,"column_start":5,"column_end":17,"is_primary":true,"text":[{"text":"    buffer_query: Query<&mut InputBufferComponent, With<Fighter>>,","highlight_start":5,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/systems.rs","byte_start":466,"byte_end":478,"line_start":17,"line_end":17,"column_start":5,"column_end":17,"is_primary":true,"text":[{"text":"    buffer_query: Query<&mut InputBufferComponent, With<Fighter>>,","highlight_start":5,"highlight_end":17}],"label":null,"suggested_replacement":"_buffer_query","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `buffer_query`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/systems.rs:17:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m17\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    buffer_query: Query<&mut InputBufferComponent, With<Fighter>>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_buffer_query`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `health`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/game_logic.rs","byte_start":5265,"byte_end":5271,"line_start":187,"line_end":187,"column_start":65,"column_end":71,"is_primary":true,"text":[{"text":"    for (entity, mut fighter_state, mut position, mut velocity, health, input_buffer) in fighter_query.iter_mut() {","highlight_start":65,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/game_logic.rs","byte_start":5265,"byte_end":5271,"line_start":187,"line_end":187,"column_start":65,"column_end":71,"is_primary":true,"text":[{"text":"    for (entity, mut fighter_state, mut position, mut velocity, health, input_buffer) in fighter_query.iter_mut() {","highlight_start":65,"highlight_end":71}],"label":null,"suggested_replacement":"_health","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `health`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/game_logic.rs:187:65\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m187\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    for (entity, mut fighter_state, mut position, mut velocity, health, input_buffer) in fighter_query.iter_mut() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_health`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `game_logic`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/game_logic.rs","byte_start":4828,"byte_end":4838,"line_start":174,"line_end":174,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    game_logic: &mut ResMut<GameLogic>,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/game_logic.rs","byte_start":4828,"byte_end":4838,"line_start":174,"line_end":174,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    game_logic: &mut ResMut<GameLogic>,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":"_game_logic","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `game_logic`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/game_logic.rs:174:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m174\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    game_logic: &mut ResMut<GameLogic>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_game_logic`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `hit_events`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/game_logic.rs","byte_start":9563,"byte_end":9573,"line_start":301,"line_end":301,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    hit_events: &mut EventWriter<HitEvent>,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/game_logic.rs","byte_start":9563,"byte_end":9573,"line_start":301,"line_end":301,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    hit_events: &mut EventWriter<HitEvent>,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":"_hit_events","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `hit_events`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/game_logic.rs:301:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m301\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    hit_events: &mut EventWriter<HitEvent>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_hit_events`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `time`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":4808,"byte_end":4812,"line_start":171,"line_end":171,"column_start":5,"column_end":9,"is_primary":true,"text":[{"text":"    time: Res<Time>,","highlight_start":5,"highlight_end":9}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":4808,"byte_end":4812,"line_start":171,"line_end":171,"column_start":5,"column_end":9,"is_primary":true,"text":[{"text":"    time: Res<Time>,","highlight_start":5,"highlight_end":9}],"label":null,"suggested_replacement":"_time","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `time`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/performance.rs:171:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m171\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    time: Res<Time>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_time`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `game_logic`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":8647,"byte_end":8657,"line_start":296,"line_end":296,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    game_logic: &mut ResMut<GameLogic>,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":8647,"byte_end":8657,"line_start":296,"line_end":296,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    game_logic: &mut ResMut<GameLogic>,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":"_game_logic","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `game_logic`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/performance.rs:296:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m296\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    game_logic: &mut ResMut<GameLogic>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_game_logic`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `special_move_events`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":8734,"byte_end":8753,"line_start":298,"line_end":298,"column_start":5,"column_end":24,"is_primary":true,"text":[{"text":"    special_move_events: &mut EventWriter<SpecialMoveEvent>,","highlight_start":5,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":8734,"byte_end":8753,"line_start":298,"line_end":298,"column_start":5,"column_end":24,"is_primary":true,"text":[{"text":"    special_move_events: &mut EventWriter<SpecialMoveEvent>,","highlight_start":5,"highlight_end":24}],"label":null,"suggested_replacement":"_special_move_events","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `special_move_events`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/performance.rs:298:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m298\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    special_move_events: &mut EventWriter<SpecialMoveEvent>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_special_move_events`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `hit_events`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":8795,"byte_end":8805,"line_start":299,"line_end":299,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    hit_events: &mut EventWriter<HitEvent>,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":8795,"byte_end":8805,"line_start":299,"line_end":299,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    hit_events: &mut EventWriter<HitEvent>,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":"_hit_events","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `hit_events`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/performance.rs:299:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m299\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    hit_events: &mut EventWriter<HitEvent>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_hit_events`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `physics_start`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":11155,"byte_end":11168,"line_start":360,"line_end":360,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    let physics_start = std::time::Instant::now();","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":11155,"byte_end":11168,"line_start":360,"line_end":360,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    let physics_start = std::time::Instant::now();","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":"_physics_start","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `physics_start`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/performance.rs:360:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m360\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let physics_start = std::time::Instant::now();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_physics_start`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `performance_config`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":11098,"byte_end":11116,"line_start":358,"line_end":358,"column_start":5,"column_end":23,"is_primary":true,"text":[{"text":"    performance_config: &Res<PerformanceConfig>,","highlight_start":5,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":11098,"byte_end":11116,"line_start":358,"line_end":358,"column_start":5,"column_end":23,"is_primary":true,"text":[{"text":"    performance_config: &Res<PerformanceConfig>,","highlight_start":5,"highlight_end":23}],"label":null,"suggested_replacement":"_performance_config","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `performance_config`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/performance.rs:358:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m358\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    performance_config: &Res<PerformanceConfig>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_performance_config`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `hit_events`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":13059,"byte_end":13069,"line_start":414,"line_end":414,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    hit_events: &mut EventWriter<HitEvent>,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":13059,"byte_end":13069,"line_start":414,"line_end":414,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    hit_events: &mut EventWriter<HitEvent>,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":"_hit_events","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `hit_events`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/performance.rs:414:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m414\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    hit_events: &mut EventWriter<HitEvent>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_hit_events`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `config`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/collision_system.rs","byte_start":4925,"byte_end":4931,"line_start":147,"line_end":147,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    config: &CollisionConfig,","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/collision_system.rs","byte_start":4925,"byte_end":4931,"line_start":147,"line_end":147,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    config: &CollisionConfig,","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":"_config","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `config`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/collision_system.rs:147:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m147\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    config: &CollisionConfig,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_config`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `collision_query`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/collision_system.rs","byte_start":11122,"byte_end":11137,"line_start":320,"line_end":320,"column_start":5,"column_end":20,"is_primary":true,"text":[{"text":"    collision_query: &CollisionQuery,","highlight_start":5,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/collision_system.rs","byte_start":11122,"byte_end":11137,"line_start":320,"line_end":320,"column_start":5,"column_end":20,"is_primary":true,"text":[{"text":"    collision_query: &CollisionQuery,","highlight_start":5,"highlight_end":20}],"label":null,"suggested_replacement":"_collision_query","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `collision_query`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/collision_system.rs:320:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m320\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    collision_query: &CollisionQuery,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_collision_query`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `config`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/collision_system.rs","byte_start":11160,"byte_end":11166,"line_start":321,"line_end":321,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    config: &CollisionConfig,","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/collision_system.rs","byte_start":11160,"byte_end":11166,"line_start":321,"line_end":321,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    config: &CollisionConfig,","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":"_config","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `config`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/collision_system.rs:321:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m321\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    config: &CollisionConfig,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_config`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `collision_query`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/collision_system.rs","byte_start":12013,"byte_end":12028,"line_start":348,"line_end":348,"column_start":5,"column_end":20,"is_primary":true,"text":[{"text":"    collision_query: &CollisionQuery,","highlight_start":5,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/collision_system.rs","byte_start":12013,"byte_end":12028,"line_start":348,"line_end":348,"column_start":5,"column_end":20,"is_primary":true,"text":[{"text":"    collision_query: &CollisionQuery,","highlight_start":5,"highlight_end":20}],"label":null,"suggested_replacement":"_collision_query","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `collision_query`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/collision_system.rs:348:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m348\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    collision_query: &CollisionQuery,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_collision_query`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `entity`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/character_system.rs","byte_start":5513,"byte_end":5519,"line_start":176,"line_end":176,"column_start":10,"column_end":16,"is_primary":true,"text":[{"text":"    for (entity, fighter, mut state, mut position, mut velocity, input) in fighter_query.iter_mut() {","highlight_start":10,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/character_system.rs","byte_start":5513,"byte_end":5519,"line_start":176,"line_end":176,"column_start":10,"column_end":16,"is_primary":true,"text":[{"text":"    for (entity, fighter, mut state, mut position, mut velocity, input) in fighter_query.iter_mut() {","highlight_start":10,"highlight_end":16}],"label":null,"suggested_replacement":"_entity","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `entity`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/character_system.rs:176:10\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m176\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    for (entity, fighter, mut state, mut position, mut velocity, input) in fighter_query.iter_mut() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_entity`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `anim_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/character_system.rs","byte_start":6501,"byte_end":6508,"line_start":192,"line_end":192,"column_start":49,"column_end":56,"is_primary":true,"text":[{"text":"                    FighterAction::SetAnimation(anim_id) => {","highlight_start":49,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/character_system.rs","byte_start":6501,"byte_end":6508,"line_start":192,"line_end":192,"column_start":49,"column_end":56,"is_primary":true,"text":[{"text":"                    FighterAction::SetAnimation(anim_id) => {","highlight_start":49,"highlight_end":56}],"label":null,"suggested_replacement":"_anim_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `anim_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/character_system.rs:192:49\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m192\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    FighterAction::SetAnimation(anim_id) => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_anim_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/character_system.rs","byte_start":8058,"byte_end":8063,"line_start":230,"line_end":230,"column_start":19,"column_end":24,"is_primary":true,"text":[{"text":"    for (fighter, state) in fighter_query.iter_mut() {","highlight_start":19,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/character_system.rs","byte_start":8058,"byte_end":8063,"line_start":230,"line_end":230,"column_start":19,"column_end":24,"is_primary":true,"text":[{"text":"    for (fighter, state) in fighter_query.iter_mut() {","highlight_start":19,"highlight_end":24}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/character_system.rs:230:19\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m230\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    for (fighter, state) in fighter_query.iter_mut() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":6477,"byte_end":6482,"line_start":188,"line_end":188,"column_start":28,"column_end":33,"is_primary":true,"text":[{"text":"    fn get_hitboxes(&self, state: &FighterStateData) -> Vec<HitboxData> {","highlight_start":28,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":6477,"byte_end":6482,"line_start":188,"line_end":188,"column_start":28,"column_end":33,"is_primary":true,"text":[{"text":"    fn get_hitboxes(&self, state: &FighterStateData) -> Vec<HitboxData> {","highlight_start":28,"highlight_end":33}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ryu.rs:188:28\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m188\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn get_hitboxes(&self, state: &FighterStateData) -> Vec<HitboxData> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":6719,"byte_end":6724,"line_start":194,"line_end":194,"column_start":29,"column_end":34,"is_primary":true,"text":[{"text":"    fn get_hurtboxes(&self, state: &FighterStateData) -> Vec<HurtboxData> {","highlight_start":29,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":6719,"byte_end":6724,"line_start":194,"line_end":194,"column_start":29,"column_end":34,"is_primary":true,"text":[{"text":"    fn get_hurtboxes(&self, state: &FighterStateData) -> Vec<HurtboxData> {","highlight_start":29,"highlight_end":34}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ryu.rs:194:29\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m194\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn get_hurtboxes(&self, state: &FighterStateData) -> Vec<HurtboxData> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":7087,"byte_end":7092,"line_start":205,"line_end":205,"column_start":43,"column_end":48,"is_primary":true,"text":[{"text":"    fn on_hit(&self, attack: &AttackData, state: &mut FighterStateData) -> HitResponse {","highlight_start":43,"highlight_end":48}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":7087,"byte_end":7092,"line_start":205,"line_end":205,"column_start":43,"column_end":48,"is_primary":true,"text":[{"text":"    fn on_hit(&self, attack: &AttackData, state: &mut FighterStateData) -> HitResponse {","highlight_start":43,"highlight_end":48}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ryu.rs:205:43\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m205\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn on_hit(&self, attack: &AttackData, state: &mut FighterStateData) -> HitResponse {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":7522,"byte_end":7527,"line_start":216,"line_end":216,"column_start":45,"column_end":50,"is_primary":true,"text":[{"text":"    fn on_block(&self, attack: &AttackData, state: &mut FighterStateData) -> BlockResponse {","highlight_start":45,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":7522,"byte_end":7527,"line_start":216,"line_end":216,"column_start":45,"column_end":50,"is_primary":true,"text":[{"text":"    fn on_block(&self, attack: &AttackData, state: &mut FighterStateData) -> BlockResponse {","highlight_start":45,"highlight_end":50}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ryu.rs:216:45\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m216\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn on_block(&self, attack: &AttackData, state: &mut FighterStateData) -> BlockResponse {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":9113,"byte_end":9118,"line_start":260,"line_end":260,"column_start":55,"column_end":60,"is_primary":true,"text":[{"text":"    fn check_special_moves(&self, input: &InputState, state: &FighterStateData) -> Option<FighterAction> {","highlight_start":55,"highlight_end":60}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":9113,"byte_end":9118,"line_start":260,"line_end":260,"column_start":55,"column_end":60,"is_primary":true,"text":[{"text":"    fn check_special_moves(&self, input: &InputState, state: &FighterStateData) -> Option<FighterAction> {","highlight_start":55,"highlight_end":60}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ryu.rs:260:55\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m260\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn check_special_moves(&self, input: &InputState, state: &FighterStateData) -> Option<FighterAction> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                       \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":10545,"byte_end":10550,"line_start":298,"line_end":298,"column_start":31,"column_end":36,"is_primary":true,"text":[{"text":"    fn execute_hadoken(&self, state: &mut FighterStateData) -> SpecialMoveResult {","highlight_start":31,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":10545,"byte_end":10550,"line_start":298,"line_end":298,"column_start":31,"column_end":36,"is_primary":true,"text":[{"text":"    fn execute_hadoken(&self, state: &mut FighterStateData) -> SpecialMoveResult {","highlight_start":31,"highlight_end":36}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ryu.rs:298:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m298\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn execute_hadoken(&self, state: &mut FighterStateData) -> SpecialMoveResult {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":10985,"byte_end":10990,"line_start":311,"line_end":311,"column_start":33,"column_end":38,"is_primary":true,"text":[{"text":"    fn execute_shoryuken(&self, state: &mut FighterStateData) -> SpecialMoveResult {","highlight_start":33,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":10985,"byte_end":10990,"line_start":311,"line_end":311,"column_start":33,"column_end":38,"is_primary":true,"text":[{"text":"    fn execute_shoryuken(&self, state: &mut FighterStateData) -> SpecialMoveResult {","highlight_start":33,"highlight_end":38}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ryu.rs:311:33\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m311\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn execute_shoryuken(&self, state: &mut FighterStateData) -> SpecialMoveResult {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":11523,"byte_end":11528,"line_start":325,"line_end":325,"column_start":33,"column_end":38,"is_primary":true,"text":[{"text":"    fn execute_tatsumaki(&self, state: &mut FighterStateData) -> SpecialMoveResult {","highlight_start":33,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":11523,"byte_end":11528,"line_start":325,"line_end":325,"column_start":33,"column_end":38,"is_primary":true,"text":[{"text":"    fn execute_tatsumaki(&self, state: &mut FighterStateData) -> SpecialMoveResult {","highlight_start":33,"highlight_end":38}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ryu.rs:325:33\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m325\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn execute_tatsumaki(&self, state: &mut FighterStateData) -> SpecialMoveResult {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":6675,"byte_end":6680,"line_start":188,"line_end":188,"column_start":28,"column_end":33,"is_primary":true,"text":[{"text":"    fn get_hitboxes(&self, state: &FighterStateData) -> Vec<HitboxData> {","highlight_start":28,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":6675,"byte_end":6680,"line_start":188,"line_end":188,"column_start":28,"column_end":33,"is_primary":true,"text":[{"text":"    fn get_hitboxes(&self, state: &FighterStateData) -> Vec<HitboxData> {","highlight_start":28,"highlight_end":33}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ken.rs:188:28\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m188\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn get_hitboxes(&self, state: &FighterStateData) -> Vec<HitboxData> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":6776,"byte_end":6781,"line_start":192,"line_end":192,"column_start":29,"column_end":34,"is_primary":true,"text":[{"text":"    fn get_hurtboxes(&self, state: &FighterStateData) -> Vec<HurtboxData> {","highlight_start":29,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":6776,"byte_end":6781,"line_start":192,"line_end":192,"column_start":29,"column_end":34,"is_primary":true,"text":[{"text":"    fn get_hurtboxes(&self, state: &FighterStateData) -> Vec<HurtboxData> {","highlight_start":29,"highlight_end":34}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ken.rs:192:29\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m192\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn get_hurtboxes(&self, state: &FighterStateData) -> Vec<HurtboxData> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":7083,"byte_end":7088,"line_start":202,"line_end":202,"column_start":43,"column_end":48,"is_primary":true,"text":[{"text":"    fn on_hit(&self, attack: &AttackData, state: &mut FighterStateData) -> HitResponse {","highlight_start":43,"highlight_end":48}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":7083,"byte_end":7088,"line_start":202,"line_end":202,"column_start":43,"column_end":48,"is_primary":true,"text":[{"text":"    fn on_hit(&self, attack: &AttackData, state: &mut FighterStateData) -> HitResponse {","highlight_start":43,"highlight_end":48}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ken.rs:202:43\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m202\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn on_hit(&self, attack: &AttackData, state: &mut FighterStateData) -> HitResponse {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":7493,"byte_end":7498,"line_start":213,"line_end":213,"column_start":45,"column_end":50,"is_primary":true,"text":[{"text":"    fn on_block(&self, attack: &AttackData, state: &mut FighterStateData) -> BlockResponse {","highlight_start":45,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":7493,"byte_end":7498,"line_start":213,"line_end":213,"column_start":45,"column_end":50,"is_primary":true,"text":[{"text":"    fn on_block(&self, attack: &AttackData, state: &mut FighterStateData) -> BlockResponse {","highlight_start":45,"highlight_end":50}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ken.rs:213:45\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m213\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn on_block(&self, attack: &AttackData, state: &mut FighterStateData) -> BlockResponse {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":9237,"byte_end":9242,"line_start":257,"line_end":257,"column_start":55,"column_end":60,"is_primary":true,"text":[{"text":"    fn check_special_moves(&self, input: &InputState, state: &FighterStateData) -> Option<FighterAction> {","highlight_start":55,"highlight_end":60}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":9237,"byte_end":9242,"line_start":257,"line_end":257,"column_start":55,"column_end":60,"is_primary":true,"text":[{"text":"    fn check_special_moves(&self, input: &InputState, state: &FighterStateData) -> Option<FighterAction> {","highlight_start":55,"highlight_end":60}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ken.rs:257:55\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m257\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn check_special_moves(&self, input: &InputState, state: &FighterStateData) -> Option<FighterAction> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                       \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":10253,"byte_end":10258,"line_start":287,"line_end":287,"column_start":31,"column_end":36,"is_primary":true,"text":[{"text":"    fn execute_hadoken(&self, state: &mut FighterStateData) -> SpecialMoveResult {","highlight_start":31,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":10253,"byte_end":10258,"line_start":287,"line_end":287,"column_start":31,"column_end":36,"is_primary":true,"text":[{"text":"    fn execute_hadoken(&self, state: &mut FighterStateData) -> SpecialMoveResult {","highlight_start":31,"highlight_end":36}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ken.rs:287:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m287\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn execute_hadoken(&self, state: &mut FighterStateData) -> SpecialMoveResult {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":10716,"byte_end":10721,"line_start":300,"line_end":300,"column_start":33,"column_end":38,"is_primary":true,"text":[{"text":"    fn execute_shoryuken(&self, state: &mut FighterStateData) -> SpecialMoveResult {","highlight_start":33,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":10716,"byte_end":10721,"line_start":300,"line_end":300,"column_start":33,"column_end":38,"is_primary":true,"text":[{"text":"    fn execute_shoryuken(&self, state: &mut FighterStateData) -> SpecialMoveResult {","highlight_start":33,"highlight_end":38}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ken.rs:300:33\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m300\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn execute_shoryuken(&self, state: &mut FighterStateData) -> SpecialMoveResult {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":11347,"byte_end":11352,"line_start":315,"line_end":315,"column_start":33,"column_end":38,"is_primary":true,"text":[{"text":"    fn execute_tatsumaki(&self, state: &mut FighterStateData) -> SpecialMoveResult {","highlight_start":33,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":11347,"byte_end":11352,"line_start":315,"line_end":315,"column_start":33,"column_end":38,"is_primary":true,"text":[{"text":"    fn execute_tatsumaki(&self, state: &mut FighterStateData) -> SpecialMoveResult {","highlight_start":33,"highlight_end":38}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ken.rs:315:33\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m315\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn execute_tatsumaki(&self, state: &mut FighterStateData) -> SpecialMoveResult {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/blanka.rs","byte_start":1690,"byte_end":1695,"line_start":58,"line_end":58,"column_start":49,"column_end":54,"is_primary":true,"text":[{"text":"    fn process_input(&self, input: &InputState, state: &mut FighterStateData) -> Vec<FighterAction> {","highlight_start":49,"highlight_end":54}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/blanka.rs","byte_start":1690,"byte_end":1695,"line_start":58,"line_end":58,"column_start":49,"column_end":54,"is_primary":true,"text":[{"text":"    fn process_input(&self, input: &InputState, state: &mut FighterStateData) -> Vec<FighterAction> {","highlight_start":49,"highlight_end":54}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/blanka.rs:58:49\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m58\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn process_input(&self, input: &InputState, state: &mut FighterStateData) -> Vec<FighterAction> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `fighter_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_core.rs","byte_start":6369,"byte_end":6379,"line_start":199,"line_end":199,"column_start":35,"column_end":45,"is_primary":true,"text":[{"text":"    pub fn init_player(&mut self, fighter_id: FighterId, difficulty: u8) {","highlight_start":35,"highlight_end":45}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_core.rs","byte_start":6369,"byte_end":6379,"line_start":199,"line_end":199,"column_start":35,"column_end":45,"is_primary":true,"text":[{"text":"    pub fn init_player(&mut self, fighter_id: FighterId, difficulty: u8) {","highlight_start":35,"highlight_end":45}],"label":null,"suggested_replacement":"_fighter_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `fighter_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_core.rs:199:35\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m199\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn init_player(&mut self, fighter_id: FighterId, difficulty: u8) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_fighter_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `difficulty`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_core.rs","byte_start":6392,"byte_end":6402,"line_start":199,"line_end":199,"column_start":58,"column_end":68,"is_primary":true,"text":[{"text":"    pub fn init_player(&mut self, fighter_id: FighterId, difficulty: u8) {","highlight_start":58,"highlight_end":68}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_core.rs","byte_start":6392,"byte_end":6402,"line_start":199,"line_end":199,"column_start":58,"column_end":68,"is_primary":true,"text":[{"text":"    pub fn init_player(&mut self, fighter_id: FighterId, difficulty: u8) {","highlight_start":58,"highlight_end":68}],"label":null,"suggested_replacement":"_difficulty","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `difficulty`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_core.rs:199:58\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m199\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn init_player(&mut self, fighter_id: FighterId, difficulty: u8) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                          \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_difficulty`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `opponent_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_strategy.rs","byte_start":3345,"byte_end":3356,"line_start":111,"line_end":111,"column_start":74,"column_end":85,"is_primary":true,"text":[{"text":"    pub fn load_character_strategies(&mut self, character_id: FighterId, opponent_id: FighterId) {","highlight_start":74,"highlight_end":85}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_strategy.rs","byte_start":3345,"byte_end":3356,"line_start":111,"line_end":111,"column_start":74,"column_end":85,"is_primary":true,"text":[{"text":"    pub fn load_character_strategies(&mut self, character_id: FighterId, opponent_id: FighterId) {","highlight_start":74,"highlight_end":85}],"label":null,"suggested_replacement":"_opponent_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `opponent_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_strategy.rs:111:74\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m111\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn load_character_strategies(&mut self, character_id: FighterId, opponent_id: FighterId) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                          \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_opponent_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `fighter_query`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_difficulty.rs","byte_start":17330,"byte_end":17343,"line_start":434,"line_end":434,"column_start":5,"column_end":18,"is_primary":true,"text":[{"text":"    fighter_query: Query<&FighterStateData, With<Fighter>>,","highlight_start":5,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_difficulty.rs","byte_start":17330,"byte_end":17343,"line_start":434,"line_end":434,"column_start":5,"column_end":18,"is_primary":true,"text":[{"text":"    fighter_query: Query<&FighterStateData, With<Fighter>>,","highlight_start":5,"highlight_end":18}],"label":null,"suggested_replacement":"_fighter_query","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `fighter_query`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_difficulty.rs:434:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m434\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fighter_query: Query<&FighterStateData, With<Fighter>>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_fighter_query`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `performance_tracker`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_difficulty.rs","byte_start":17411,"byte_end":17430,"line_start":436,"line_end":436,"column_start":5,"column_end":24,"is_primary":true,"text":[{"text":"    performance_tracker: Local<HashMap<Entity, AIPerformanceMetrics>>,","highlight_start":5,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_difficulty.rs","byte_start":17411,"byte_end":17430,"line_start":436,"line_end":436,"column_start":5,"column_end":24,"is_primary":true,"text":[{"text":"    performance_tracker: Local<HashMap<Entity, AIPerformanceMetrics>>,","highlight_start":5,"highlight_end":24}],"label":null,"suggested_replacement":"_performance_tracker","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `performance_tracker`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_difficulty.rs:436:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m436\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    performance_tracker: Local<HashMap<Entity, AIPerformanceMetrics>>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_performance_tracker`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `fighter_query`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_difficulty.rs","byte_start":17930,"byte_end":17943,"line_start":450,"line_end":450,"column_start":5,"column_end":18,"is_primary":true,"text":[{"text":"    fighter_query: Query<&FighterStateData, With<Fighter>>,","highlight_start":5,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_difficulty.rs","byte_start":17930,"byte_end":17943,"line_start":450,"line_end":450,"column_start":5,"column_end":18,"is_primary":true,"text":[{"text":"    fighter_query: Query<&FighterStateData, With<Fighter>>,","highlight_start":5,"highlight_end":18}],"label":null,"suggested_replacement":"_fighter_query","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `fighter_query`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_difficulty.rs:450:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m450\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fighter_query: Query<&FighterStateData, With<Fighter>>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_fighter_query`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `frame_count`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_threat.rs","byte_start":24439,"byte_end":24450,"line_start":752,"line_end":752,"column_start":84,"column_end":95,"is_primary":true,"text":[{"text":"    fn update_opponent_tracking(&mut self, threat_assessment: &AIThreatAssessment, frame_count: u64) {","highlight_start":84,"highlight_end":95}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_threat.rs","byte_start":24439,"byte_end":24450,"line_start":752,"line_end":752,"column_start":84,"column_end":95,"is_primary":true,"text":[{"text":"    fn update_opponent_tracking(&mut self, threat_assessment: &AIThreatAssessment, frame_count: u64) {","highlight_start":84,"highlight_end":95}],"label":null,"suggested_replacement":"_frame_count","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `frame_count`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_threat.rs:752:84\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m752\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn update_opponent_tracking(&mut self, threat_assessment: &AIThreatAssessment, frame_count: u64) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                                    \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_frame_count`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `decision`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_threat.rs","byte_start":47144,"byte_end":47152,"line_start":1337,"line_end":1337,"column_start":21,"column_end":29,"is_primary":true,"text":[{"text":"        if let Some(decision) = decision_maker.make_decision(","highlight_start":21,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_threat.rs","byte_start":47144,"byte_end":47152,"line_start":1337,"line_end":1337,"column_start":21,"column_end":29,"is_primary":true,"text":[{"text":"        if let Some(decision) = decision_maker.make_decision(","highlight_start":21,"highlight_end":29}],"label":null,"suggested_replacement":"_decision","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `decision`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_threat.rs:1337:21\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1337\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        if let Some(decision) = decision_maker.make_decision(\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_decision`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `frame`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_debug.rs","byte_start":8483,"byte_end":8488,"line_start":299,"line_end":299,"column_start":61,"column_end":66,"is_primary":true,"text":[{"text":"    pub fn record_decision(&mut self, decision: AIDecision, frame: u64) {","highlight_start":61,"highlight_end":66}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_debug.rs","byte_start":8483,"byte_end":8488,"line_start":299,"line_end":299,"column_start":61,"column_end":66,"is_primary":true,"text":[{"text":"    pub fn record_decision(&mut self, decision: AIDecision, frame: u64) {","highlight_start":61,"highlight_end":66}],"label":null,"suggested_replacement":"_frame","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `frame`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_debug.rs:299:61\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m299\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn record_decision(&mut self, decision: AIDecision, frame: u64) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_frame`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `round_duration_frames`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_debug.rs","byte_start":11234,"byte_end":11255,"line_start":382,"line_end":382,"column_start":56,"column_end":77,"is_primary":true,"text":[{"text":"    pub fn update_win_loss_stats(&mut self, won: bool, round_duration_frames: u32) {","highlight_start":56,"highlight_end":77}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_debug.rs","byte_start":11234,"byte_end":11255,"line_start":382,"line_end":382,"column_start":56,"column_end":77,"is_primary":true,"text":[{"text":"    pub fn update_win_loss_stats(&mut self, won: bool, round_duration_frames: u32) {","highlight_start":56,"highlight_end":77}],"label":null,"suggested_replacement":"_round_duration_frames","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `round_duration_frames`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_debug.rs:382:56\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m382\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn update_win_loss_stats(&mut self, won: bool, round_duration_frames: u32) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_round_duration_frames`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `entity`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_debug.rs","byte_start":25119,"byte_end":25125,"line_start":778,"line_end":778,"column_start":10,"column_end":16,"is_primary":true,"text":[{"text":"    for (entity, mut debug_info, ai_core, difficulty, threat_assessment, decision_maker) in debug_query.iter_mut() {","highlight_start":10,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_debug.rs","byte_start":25119,"byte_end":25125,"line_start":778,"line_end":778,"column_start":10,"column_end":16,"is_primary":true,"text":[{"text":"    for (entity, mut debug_info, ai_core, difficulty, threat_assessment, decision_maker) in debug_query.iter_mut() {","highlight_start":10,"highlight_end":16}],"label":null,"suggested_replacement":"_entity","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `entity`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_debug.rs:778:10\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m778\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    for (entity, mut debug_info, ai_core, difficulty, threat_assessment, decision_maker) in debug_query.iter_mut() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_entity`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `difficulty`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_debug.rs","byte_start":25152,"byte_end":25162,"line_start":778,"line_end":778,"column_start":43,"column_end":53,"is_primary":true,"text":[{"text":"    for (entity, mut debug_info, ai_core, difficulty, threat_assessment, decision_maker) in debug_query.iter_mut() {","highlight_start":43,"highlight_end":53}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_debug.rs","byte_start":25152,"byte_end":25162,"line_start":778,"line_end":778,"column_start":43,"column_end":53,"is_primary":true,"text":[{"text":"    for (entity, mut debug_info, ai_core, difficulty, threat_assessment, decision_maker) in debug_query.iter_mut() {","highlight_start":43,"highlight_end":53}],"label":null,"suggested_replacement":"_difficulty","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `difficulty`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_debug.rs:778:43\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m778\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    for (entity, mut debug_info, ai_core, difficulty, threat_assessment, decision_maker) in debug_query.iter_mut() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_difficulty`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `p`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_character_patterns.rs","byte_start":24371,"byte_end":24372,"line_start":623,"line_end":623,"column_start":22,"column_end":23,"is_primary":true,"text":[{"text":"            .filter(|p| difficulty >= 3) // Only use counters at higher difficulty","highlight_start":22,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_character_patterns.rs","byte_start":24371,"byte_end":24372,"line_start":623,"line_end":623,"column_start":22,"column_end":23,"is_primary":true,"text":[{"text":"            .filter(|p| difficulty >= 3) // Only use counters at higher difficulty","highlight_start":22,"highlight_end":23}],"label":null,"suggested_replacement":"_p","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `p`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_character_patterns.rs:623:22\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m623\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .filter(|p| difficulty >= 3) // Only use counters at higher difficulty\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_p`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `current_frame`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_character_patterns.rs","byte_start":28800,"byte_end":28813,"line_start":730,"line_end":730,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    let current_frame = (time.elapsed_secs() * 60.0) as u64; // Assuming 60 FPS","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_character_patterns.rs","byte_start":28800,"byte_end":28813,"line_start":730,"line_end":730,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    let current_frame = (time.elapsed_secs() * 60.0) as u64; // Assuming 60 FPS","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":"_current_frame","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `current_frame`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_character_patterns.rs:730:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m730\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let current_frame = (time.elapsed_secs() * 60.0) as u64; // Assuming 60 FPS\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_current_frame`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable `failure_count` is assigned to, but never used","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_behavior_tree.rs","byte_start":23120,"byte_end":23133,"line_start":720,"line_end":720,"column_start":17,"column_end":30,"is_primary":true,"text":[{"text":"        let mut failure_count = 0;","highlight_start":17,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider using `_failure_count` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variable `failure_count` is assigned to, but never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_behavior_tree.rs:720:17\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m720\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut failure_count = 0;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: consider using `_failure_count` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `attack_type`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_behavior_tree.rs","byte_start":27674,"byte_end":27685,"line_start":844,"line_end":844,"column_start":30,"column_end":41,"is_primary":true,"text":[{"text":"            AIAction::Attack(attack_type) => {","highlight_start":30,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_behavior_tree.rs","byte_start":27674,"byte_end":27685,"line_start":844,"line_end":844,"column_start":30,"column_end":41,"is_primary":true,"text":[{"text":"            AIAction::Attack(attack_type) => {","highlight_start":30,"highlight_end":41}],"label":null,"suggested_replacement":"_attack_type","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `attack_type`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_behavior_tree.rs:844:30\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m844\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            AIAction::Attack(attack_type) => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_attack_type`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `special_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_behavior_tree.rs","byte_start":27889,"byte_end":27899,"line_start":849,"line_end":849,"column_start":35,"column_end":45,"is_primary":true,"text":[{"text":"            AIAction::SpecialMove(special_id) => {","highlight_start":35,"highlight_end":45}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_behavior_tree.rs","byte_start":27889,"byte_end":27899,"line_start":849,"line_end":849,"column_start":35,"column_end":45,"is_primary":true,"text":[{"text":"            AIAction::SpecialMove(special_id) => {","highlight_start":35,"highlight_end":45}],"label":null,"suggested_replacement":"_special_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `special_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_behavior_tree.rs:849:35\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m849\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            AIAction::SpecialMove(special_id) => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_special_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `context`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_behavior_tree.rs","byte_start":29709,"byte_end":29716,"line_start":898,"line_end":898,"column_start":9,"column_end":16,"is_primary":true,"text":[{"text":"        context: &mut BehaviorExecutionContext,","highlight_start":9,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_behavior_tree.rs","byte_start":29709,"byte_end":29716,"line_start":898,"line_end":898,"column_start":9,"column_end":16,"is_primary":true,"text":[{"text":"        context: &mut BehaviorExecutionContext,","highlight_start":9,"highlight_end":16}],"label":null,"suggested_replacement":"_context","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `context`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_behavior_tree.rs:898:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m898\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        context: &mut BehaviorExecutionContext,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_context`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `context`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_behavior_tree.rs","byte_start":31054,"byte_end":31061,"line_start":937,"line_end":937,"column_start":9,"column_end":16,"is_primary":true,"text":[{"text":"        context: &mut BehaviorExecutionContext,","highlight_start":9,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_behavior_tree.rs","byte_start":31054,"byte_end":31061,"line_start":937,"line_end":937,"column_start":9,"column_end":16,"is_primary":true,"text":[{"text":"        context: &mut BehaviorExecutionContext,","highlight_start":9,"highlight_end":16}],"label":null,"suggested_replacement":"_context","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `context`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_behavior_tree.rs:937:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m937\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        context: &mut BehaviorExecutionContext,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_context`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `ai_core`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_behavior_tree.rs","byte_start":32380,"byte_end":32387,"line_start":969,"line_end":969,"column_start":40,"column_end":47,"is_primary":true,"text":[{"text":"    fn has_projectile_advantage(&self, ai_core: &AICore, context: &BehaviorExecutionContext) -> bool {","highlight_start":40,"highlight_end":47}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_behavior_tree.rs","byte_start":32380,"byte_end":32387,"line_start":969,"line_end":969,"column_start":40,"column_end":47,"is_primary":true,"text":[{"text":"    fn has_projectile_advantage(&self, ai_core: &AICore, context: &BehaviorExecutionContext) -> bool {","highlight_start":40,"highlight_end":47}],"label":null,"suggested_replacement":"_ai_core","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `ai_core`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_behavior_tree.rs:969:40\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m969\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn has_projectile_advantage(&self, ai_core: &AICore, context: &BehaviorExecutionContext) -> bool {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_ai_core`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `ai_core`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_behavior_tree.rs","byte_start":32698,"byte_end":32705,"line_start":976,"line_end":976,"column_start":27,"column_end":34,"is_primary":true,"text":[{"text":"    fn is_cornered(&self, ai_core: &AICore, context: &BehaviorExecutionContext) -> bool {","highlight_start":27,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_behavior_tree.rs","byte_start":32698,"byte_end":32705,"line_start":976,"line_end":976,"column_start":27,"column_end":34,"is_primary":true,"text":[{"text":"    fn is_cornered(&self, ai_core: &AICore, context: &BehaviorExecutionContext) -> bool {","highlight_start":27,"highlight_end":34}],"label":null,"suggested_replacement":"_ai_core","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `ai_core`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_behavior_tree.rs:976:27\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m976\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn is_cornered(&self, ai_core: &AICore, context: &BehaviorExecutionContext) -> bool {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_ai_core`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `ai_core`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_behavior_tree.rs","byte_start":33296,"byte_end":33303,"line_start":988,"line_end":988,"column_start":28,"column_end":35,"is_primary":true,"text":[{"text":"    fn should_throw(&self, ai_core: &AICore, context: &BehaviorExecutionContext) -> bool {","highlight_start":28,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_behavior_tree.rs","byte_start":33296,"byte_end":33303,"line_start":988,"line_end":988,"column_start":28,"column_end":35,"is_primary":true,"text":[{"text":"    fn should_throw(&self, ai_core: &AICore, context: &BehaviorExecutionContext) -> bool {","highlight_start":28,"highlight_end":35}],"label":null,"suggested_replacement":"_ai_core","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `ai_core`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_behavior_tree.rs:988:28\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m988\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn should_throw(&self, ai_core: &AICore, context: &BehaviorExecutionContext) -> bool {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_ai_core`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `time`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/ai/ai_behavior_tree.rs","byte_start":33832,"byte_end":33836,"line_start":1005,"line_end":1005,"column_start":5,"column_end":9,"is_primary":true,"text":[{"text":"    time: Res<Time>,","highlight_start":5,"highlight_end":9}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/ai/ai_behavior_tree.rs","byte_start":33832,"byte_end":33836,"line_start":1005,"line_end":1005,"column_start":5,"column_end":9,"is_primary":true,"text":[{"text":"    time: Res<Time>,","highlight_start":5,"highlight_end":9}],"label":null,"suggested_replacement":"_time","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `time`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/ai/ai_behavior_tree.rs:1005:5\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1005\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    time: Res<Time>,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_time`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `progress`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/qa_testing.rs","byte_start":9900,"byte_end":9908,"line_start":305,"line_end":305,"column_start":5,"column_end":13,"is_primary":true,"text":[{"text":"    progress: &AssetExtractionProgress,","highlight_start":5,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/qa_testing.rs","byte_start":9900,"byte_end":9908,"line_start":305,"line_end":305,"column_start":5,"column_end":13,"is_primary":true,"text":[{"text":"    progress: &AssetExtractionProgress,","highlight_start":5,"highlight_end":13}],"label":null,"suggested_replacement":"_progress","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `progress`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/qa_testing.rs:305:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m305\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    progress: &AssetExtractionProgress,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_progress`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `asset_server`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/lib.rs","byte_start":4162,"byte_end":4174,"line_start":130,"line_end":130,"column_start":49,"column_end":61,"is_primary":true,"text":[{"text":"fn setup_test_fighters(commands: &mut Commands, asset_server: &Res<AssetServer>) {","highlight_start":49,"highlight_end":61}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/lib.rs","byte_start":4162,"byte_end":4174,"line_start":130,"line_end":130,"column_start":49,"column_end":61,"is_primary":true,"text":[{"text":"fn setup_test_fighters(commands: &mut Commands, asset_server: &Res<AssetServer>) {","highlight_start":49,"highlight_end":61}],"label":null,"suggested_replacement":"_asset_server","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `asset_server`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/lib.rs:130:49\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m130\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn setup_test_fighters(commands: &mut Commands, asset_server: &Res<AssetServer>) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_asset_server`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"62 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 62 warnings emitted\u001b[0m\n\n"}
