//! # Street Fighter II - Rust/Bevy Port
//! 
//! Main executable for the Street Fighter II game engine built with Rust and Bevy.

use bevy::prelude::*;
use sf2_engine::SF2EnginePlugin;
use sf2_assets::SF2AssetsPlugin;

fn main() -> anyhow::Result<()> {
    info!("Starting Street Fighter II - Rust/Bevy Port");

    App::new()
        // Bevy default plugins with custom window settings
        .add_plugins(DefaultPlugins.set(WindowPlugin {
            primary_window: Some(Window {
                title: "Street Fighter II - Rust/Bevy Port".into(),
                resolution: (1024.0, 768.0).into(),
                resizable: true,
                ..default()
            }),
            ..default()
        }))
        
        // SF2 specific plugins
        .add_plugins((
            SF2AssetsPlugin::default(),
            SF2EnginePlugin,
        ))
        
        // Development plugins
        .add_systems(Startup, setup_dev_environment)
        .add_systems(Update, handle_dev_input)
        
        .run();
    
    Ok(())
}

/// Set up development environment
fn setup_dev_environment(mut commands: Commands) {
    info!("Setting up development environment");
    
    // Add development-specific setup here
    // For now, just log that we're in dev mode
    #[cfg(debug_assertions)]
    {
        info!("Running in development mode");
        commands.insert_resource(DevMode(true));
    }
    
    #[cfg(not(debug_assertions))]
    {
        info!("Running in release mode");
        commands.insert_resource(DevMode(false));
    }
}

/// Handle development input (F1 for help, etc.)
fn handle_dev_input(
    keyboard: Res<ButtonInput<KeyCode>>,
    dev_mode: Res<DevMode>,
) {
    if !dev_mode.0 {
        return;
    }
    
    if keyboard.just_pressed(KeyCode::F1) {
        info!("=== SF2 Development Controls ===");
        info!("F1: Show this help");
        info!("F2: Toggle debug overlay (TODO)");
        info!("F3: Reload assets (TODO)");
        info!("ESC: Exit game");
    }
    
    if keyboard.just_pressed(KeyCode::Escape) {
        info!("Exiting game...");
        std::process::exit(0);
    }
}

/// Resource to track if we're in development mode
#[derive(Resource)]
struct DevMode(bool);
