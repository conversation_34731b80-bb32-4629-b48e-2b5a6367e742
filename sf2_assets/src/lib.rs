//! # SF2 Assets
//!
//! Asset loading and management system for Street Fighter II ROM data.
//!
//! This crate handles loading original ROM files, validating their integrity,
//! and converting them to Bevy-compatible asset formats.

use bevy::prelude::*;
use std::path::PathBuf;
use std::env;
use std::collections::HashMap;
use log::{info, error, warn};

pub mod rom_loader;
pub mod sprite_extractor;
pub mod audio_extractor;
pub mod audio_system;
pub mod validation;
pub mod test_rom_system;

pub use rom_loader::*;
pub use sprite_extractor::*;
pub use audio_extractor::*;
pub use audio_system::*;
pub use validation::*;
pub use test_rom_system::*;

/// Plugin for SF2 asset management
pub struct SF2AssetsPlugin {
    pub rom_directory: Option<PathBuf>,
}

impl Default for SF2AssetsPlugin {
    fn default() -> Self {
        Self {
            rom_directory: None,
        }
    }
}

impl SF2AssetsPlugin {
    /// Create plugin with custom ROM directory
    pub fn with_rom_directory<P: Into<PathBuf>>(rom_directory: P) -> Self {
        Self {
            rom_directory: Some(rom_directory.into()),
        }
    }
}

impl Plugin for SF2AssetsPlugin {
    fn build(&self, app: &mut App) {
        // Determine ROM directory
        let rom_dir = self.rom_directory.clone()
            .or_else(|| env::var("SF2_ROM_DIR").ok().map(PathBuf::from))
            .unwrap_or_else(|| PathBuf::from("rom_data"));

        app
            .add_plugins(SF2AudioSystemPlugin)
            .insert_resource(RomConfig { rom_directory: rom_dir })
            .init_resource::<RomData>()
            .init_resource::<AssetExtractionProgress>()
            .init_resource::<AssetExtractionConfig>()
            .init_resource::<ExtractedSprites>()
            .init_resource::<ExtractedAudio>()
            .init_resource::<SpriteExtractorResource>()
            .init_resource::<AudioExtractorResource>()
            .init_resource::<AudioSampleManager>()
            .add_systems(Startup, load_rom_data)
            .add_systems(Update, (
                monitor_asset_extraction,
                update_extraction_progress,
                extract_sprites_system,
                extract_audio_system,
            ));
    }
}

/// Configuration for ROM loading
#[derive(Resource)]
pub struct RomConfig {
    pub rom_directory: PathBuf,
}

/// Resource containing loaded ROM data
#[derive(Resource, Default)]
pub struct RomData {
    pub loaded_roms: Option<LoadedRoms>,
    pub is_validated: bool,
    pub loading_error: Option<String>,
}

/// Resource tracking asset extraction progress
#[derive(Resource, Default)]
pub struct AssetExtractionProgress {
    pub sprites_extracted: usize,
    pub total_sprites: usize,
    pub audio_extracted: usize,
    pub total_audio: usize,
    pub is_complete: bool,
    pub extraction_started: bool,
}

/// Extracted sprite assets ready for use
#[derive(Resource, Default)]
pub struct ExtractedSprites {
    pub sprites: HashMap<u32, ExtractedSprite>,
    pub palettes: Vec<SpritePalette>,
}

/// Extracted audio assets ready for use
#[derive(Resource, Default)]
pub struct ExtractedAudio {
    pub samples: HashMap<u32, ExtractedAudioSample>,
    pub audio_handles: HashMap<String, Handle<AudioSource>>,
}

/// Resource to hold sprite extractor instance
#[derive(Resource)]
pub struct SpriteExtractorResource {
    pub extractor: Option<SpriteExtractor>,
}

impl Default for SpriteExtractorResource {
    fn default() -> Self {
        Self { extractor: None }
    }
}

/// Resource to hold audio extractor instance
#[derive(Resource)]
pub struct AudioExtractorResource {
    pub extractor: Option<AudioExtractor>,
}

impl Default for AudioExtractorResource {
    fn default() -> Self {
        Self { extractor: None }
    }
}

/// Asset extraction configuration
#[derive(Resource)]
pub struct AssetExtractionConfig {
    pub extract_sprites: bool,
    pub extract_audio: bool,
    pub max_sprites_per_frame: usize,
    pub max_audio_per_frame: usize,
}

impl Default for AssetExtractionConfig {
    fn default() -> Self {
        Self {
            extract_sprites: true,
            extract_audio: true,
            max_sprites_per_frame: 50, // Increased for faster extraction
            max_audio_per_frame: 10,   // Increased for faster extraction
        }
    }
}

/// System to load ROM data on startup
fn load_rom_data(
    rom_config: Res<RomConfig>,
    mut rom_data: ResMut<RomData>,
    mut progress: ResMut<AssetExtractionProgress>,
) {
    info!("Loading ROM data from: {}", rom_config.rom_directory.display());

    // Create ROM loader
    let loader = RomLoader::new(&rom_config.rom_directory);

    // Validate ROM files exist
    match loader.validate_rom_files() {
        Ok(()) => {
            info!("ROM file validation passed");
        }
        Err(e) => {
            error!("ROM file validation failed: {}", e);
            rom_data.loading_error = Some(format!("ROM validation failed: {}", e));
            return;
        }
    }

    // Load all ROM files
    match loader.load_all_roms() {
        Ok(loaded_roms) => {
            let stats = loaded_roms.get_stats();
            info!("Successfully loaded ROM data:");
            info!("  Graphics: {} bytes", stats.graphics_size);
            info!("  Code: {} bytes", stats.code_size);
            info!("  Audio: {} bytes", stats.audio_size);
            info!("  Sound: {} bytes", stats.sound_size);
            info!("  Total: {} bytes", stats.total_size);

            // Validate ROM integrity
            match RomValidator::validate_all(&loaded_roms) {
                Ok(()) => {
                    info!("ROM integrity validation passed");
                    rom_data.is_validated = true;
                }
                Err(e) => {
                    warn!("ROM integrity validation failed: {}", e);
                    // Continue anyway for development
                    rom_data.is_validated = false;
                }
            }

            // Initialize extraction progress with real ROM analysis
            let sprite_extractor = sprite_extractor::SpriteExtractor::new(loaded_roms.graphics_rom.clone());
            progress.total_sprites = sprite_extractor.get_total_sprite_count();
            info!("Detected {} sprites in graphics ROM", progress.total_sprites);

            if let Some(ref audio_rom) = loaded_roms.audio_rom {
                let audio_extractor = audio_extractor::AudioExtractor::new(audio_rom.clone());
                progress.total_audio = audio_extractor.get_total_sample_count();
                info!("Detected {} audio samples in audio ROM", progress.total_audio);
            } else {
                progress.total_audio = 0;
                warn!("No audio ROM found for audio analysis");
            }

            rom_data.loaded_roms = Some(loaded_roms);

            progress.extraction_started = true;

            info!("ROM data loading complete");
        }
        Err(e) => {
            error!("Failed to load ROM data: {}", e);
            rom_data.loading_error = Some(format!("ROM loading failed: {}", e));
        }
    }
}

/// System to monitor asset extraction progress
fn monitor_asset_extraction(progress: Res<AssetExtractionProgress>) {
    if progress.is_changed() && !progress.is_complete && progress.extraction_started {
        let sprite_progress = if progress.total_sprites > 0 {
            (progress.sprites_extracted as f32 / progress.total_sprites as f32) * 100.0
        } else {
            0.0
        };

        let audio_progress = if progress.total_audio > 0 {
            (progress.audio_extracted as f32 / progress.total_audio as f32) * 100.0
        } else {
            0.0
        };

        info!(
            "Asset extraction progress - Sprites: {:.1}% ({}/{}), Audio: {:.1}% ({}/{})",
            sprite_progress, progress.sprites_extracted, progress.total_sprites,
            audio_progress, progress.audio_extracted, progress.total_audio
        );
    }
}

/// System to update extraction progress
fn update_extraction_progress(mut progress: ResMut<AssetExtractionProgress>) {
    if progress.extraction_started && !progress.is_complete {
        // Check if extraction is complete
        if progress.sprites_extracted >= progress.total_sprites &&
           progress.audio_extracted >= progress.total_audio {
            progress.is_complete = true;
            info!("Asset extraction complete!");
        }
    }
}

/// System to extract sprites from ROM data
fn extract_sprites_system(
    rom_data: Res<RomData>,
    config: Res<AssetExtractionConfig>,
    mut extracted_sprites: ResMut<ExtractedSprites>,
    mut progress: ResMut<AssetExtractionProgress>,
    mut sprite_extractor_res: ResMut<SpriteExtractorResource>,
) {
    // Only extract if ROM is loaded and sprites extraction is enabled
    if !config.extract_sprites || !progress.extraction_started || progress.is_complete {
        return;
    }

    let Some(ref loaded_roms) = rom_data.loaded_roms else {
        return;
    };

    // Skip if all sprites have been extracted
    if progress.sprites_extracted >= progress.total_sprites {
        return;
    }

    // Initialize sprite extractor on first run
    if sprite_extractor_res.extractor.is_none() {
        info!("Initializing sprite extractor from ROM data");

        // Use graphics ROM data directly
        let graphics_data = loaded_roms.graphics_rom.clone();
        let mut sprite_extractor = SpriteExtractor::new(graphics_data);

        // Extract palettes first
        match sprite_extractor.extract_palettes() {
            Ok(()) => {
                info!("Successfully extracted {} palettes", sprite_extractor.palette_count());
                // Store palettes in extracted_sprites for easy access
                for i in 0..sprite_extractor.palette_count() {
                    if let Some(palette) = sprite_extractor.get_palette(i as u16) {
                        extracted_sprites.palettes.push(palette.clone());
                    }
                }
                // Store the extractor for future use
                sprite_extractor_res.extractor = Some(sprite_extractor);
            }
            Err(e) => {
                error!("Failed to extract palettes: {}", e);
                return;
            }
        }
    }

    // Continue extracting sprites if we have an extractor and haven't finished sprites
    if let Some(ref sprite_extractor) = sprite_extractor_res.extractor {
        if progress.sprites_extracted < progress.total_sprites {
            // Extract a few sprites per frame to avoid blocking
            let sprites_to_extract = config.max_sprites_per_frame.min(
                progress.total_sprites - progress.sprites_extracted
            );

            for i in 0..sprites_to_extract {
                let sprite_id = progress.sprites_extracted as u32 + i as u32;

                match sprite_extractor.extract_sprite(sprite_id) {
                    Ok(sprite) => {
                        extracted_sprites.sprites.insert(sprite_id, sprite);
                        progress.sprites_extracted += 1;
                    }
                    Err(e) => {
                        warn!("Failed to extract sprite {}: {}", sprite_id, e);
                        progress.sprites_extracted += 1; // Skip this sprite
                    }
                }
            }

            if progress.sprites_extracted >= progress.total_sprites {
                info!("Sprite extraction completed: {}/{} sprites extracted",
                      progress.sprites_extracted, progress.total_sprites);
            }
        }
    }
}

/// System to extract audio from ROM data
fn extract_audio_system(
    rom_data: Res<RomData>,
    config: Res<AssetExtractionConfig>,
    mut extracted_audio: ResMut<ExtractedAudio>,
    mut progress: ResMut<AssetExtractionProgress>,
    mut audio_extractor_res: ResMut<AudioExtractorResource>,
    _audio_manager: ResMut<AudioSampleManager>,
    _asset_server: Res<AssetServer>,
) {
    // Only extract if ROM is loaded and audio extraction is enabled
    if !config.extract_audio || !progress.extraction_started || progress.is_complete {
        return;
    }

    let Some(ref loaded_roms) = rom_data.loaded_roms else {
        return;
    };

    // Skip if all audio samples have been extracted
    if progress.audio_extracted >= progress.total_audio {
        return;
    }

    // Use audio ROM data if available
    let audio_data = if let Some(ref audio_rom) = loaded_roms.audio_rom {
        audio_rom.clone()
    } else {
        warn!("No audio ROM data available");
        return;
    };

    // Initialize audio extractor on first run
    if audio_extractor_res.extractor.is_none() {
        info!("Initializing audio extractor from ROM data");

        let mut audio_extractor = AudioExtractor::new(audio_data);

        // Extract audio metadata
        match audio_extractor.extract_sample_metadata() {
            Ok(()) => {
                info!("Successfully extracted audio metadata for {} samples",
                      audio_extractor.get_all_samples().len());
                // Store the extractor for future use
                audio_extractor_res.extractor = Some(audio_extractor);
            }
            Err(e) => {
                error!("Failed to extract audio metadata: {}", e);
                return;
            }
        }
    }

    // Continue extracting audio samples if we have an extractor and haven't finished
    if let Some(ref audio_extractor) = audio_extractor_res.extractor {
        if progress.audio_extracted < progress.total_audio {
            // Extract a few audio samples per frame to avoid blocking
            let audio_to_extract = config.max_audio_per_frame.min(
                progress.total_audio - progress.audio_extracted
            );

            for i in 0..audio_to_extract {
                let sample_id = progress.audio_extracted as u32 + i as u32;

                match audio_extractor.extract_sample(sample_id) {
                    Ok(sample) => {
                        let sample_name = sample.metadata.name.clone();
                        extracted_audio.samples.insert(sample_id, sample);
                        progress.audio_extracted += 1;

                        // TODO: Convert to Bevy AudioSource and add to asset_server
                        // This would require creating a custom audio asset loader
                        info!("Extracted audio sample: {}", sample_name);
                    }
                    Err(e) => {
                        warn!("Failed to extract audio sample {}: {}", sample_id, e);
                        progress.audio_extracted += 1; // Skip this sample
                    }
                }
            }

            if progress.audio_extracted >= progress.total_audio {
                info!("Audio extraction completed: {}/{} samples extracted",
                      progress.audio_extracted, progress.total_audio);
            }
        }
    }
}
