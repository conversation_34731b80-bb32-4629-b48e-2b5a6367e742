//! # Audio Extractor
//! 
//! Extracts audio data from Street Fighter II ROM files and converts
//! them to Bevy-compatible audio formats.

use bevy::prelude::*;
use thiserror::Error;
use log::{info, warn};

/// Errors that can occur during audio extraction
#[derive(Error, Debug)]
pub enum AudioExtractionError {
    #[error("Invalid audio data at offset {offset:08x}")]
    InvalidAudioData { offset: usize },
    
    #[error("Unsupported audio format: {format}")]
    UnsupportedFormat { format: String },
    
    #[error("Audio data too short: needed {needed}, available {available}")]
    InsufficientData { needed: usize, available: usize },
    
    #[error("Audio conversion failed: {reason}")]
    ConversionFailed { reason: String },
}

/// Audio sample metadata
#[derive(Debug, <PERSON>lone)]
pub struct AudioSampleMetadata {
    pub id: u32,
    pub name: String,
    pub sample_rate: u32,
    pub channels: u8,
    pub bit_depth: u8,
    pub data_offset: u32,
    pub data_size: u32,
}

/// Extracted audio sample ready for Bevy
#[derive(Debug)]
pub struct ExtractedAudioSample {
    pub metadata: AudioSampleMetadata,
    pub pcm_data: Vec<f32>, // Normalized PCM data
}

/// CPS1 Audio ROM structure constants
const CPS1_AUDIO_SAMPLE_RATE: u32 = 7575; // OKI MSM6295 standard sample rate
const CPS1_AUDIO_CHANNELS: u8 = 1; // Mono audio
const CPS1_AUDIO_BIT_DEPTH: u8 = 8; // 8-bit PCM (not ADPCM for SF2)

/// SF2 Audio ROM layout - SF2 uses raw audio data, not a structured table
const SF2_ESTIMATED_SAMPLE_SIZE: usize = 4096; // Estimated average sample size
const SF2_MIN_SAMPLE_SIZE: usize = 512;  // Minimum sample size
const SF2_MAX_SAMPLE_SIZE: usize = 16384; // Maximum sample size

/// Audio extractor for SF2 ROM data
pub struct AudioExtractor {
    audio_rom: Vec<u8>,
    samples: Vec<AudioSampleMetadata>,
    total_samples: usize,
}

impl AudioExtractor {
    /// Create a new audio extractor
    pub fn new(audio_rom: Vec<u8>) -> Self {
        // Analyze ROM to determine actual sample count
        let total_samples = Self::analyze_audio_rom(&audio_rom);

        info!("SF2 Audio ROM Analysis:");
        info!("  ROM Size: {} bytes", audio_rom.len());
        info!("  Estimated Samples: {}", total_samples);

        Self {
            audio_rom,
            samples: Vec::new(),
            total_samples,
        }
    }

    /// Analyze audio ROM to determine sample count
    fn analyze_audio_rom(rom_data: &[u8]) -> usize {
        if rom_data.is_empty() {
            return 0;
        }

        // SF2 audio ROMs contain raw PCM data, not a structured table
        // Estimate sample count based on ROM size and typical sample sizes
        let total_size = rom_data.len();

        // SF2 typically has around 63 audio samples of varying sizes
        // Estimate based on average sample size
        let estimated_count = (total_size / SF2_ESTIMATED_SAMPLE_SIZE).max(1).min(128);

        info!("Audio ROM analysis: {} bytes total, estimated {} samples",
              total_size, estimated_count);

        estimated_count
    }

    /// Get total number of audio samples
    pub fn get_total_sample_count(&self) -> usize {
        self.total_samples
    }
    
    /// Extract audio sample metadata from ROM
    pub fn extract_sample_metadata(&mut self) -> Result<(), AudioExtractionError> {
        info!("Extracting SF2 audio sample metadata from ROM");

        if self.audio_rom.is_empty() {
            return Err(AudioExtractionError::InsufficientData {
                needed: 1,
                available: 0,
            });
        }

        self.samples.clear();

        // SF2 audio ROMs contain raw PCM data without a structured table
        // Create estimated samples based on ROM size and typical SF2 audio layout
        self.create_estimated_samples()?;

        info!("Created {} estimated audio samples from SF2 ROM", self.samples.len());
        Ok(())
    }

    /// Generate sample name based on ID
    fn generate_sample_name(sample_id: u32) -> String {
        // Generate meaningful names based on typical SF2 audio layout
        match sample_id {
            0..=15 => format!("voice_{:02}", sample_id),      // Character voices
            16..=31 => format!("sfx_{:02}", sample_id - 16),  // Sound effects
            32..=47 => format!("music_{:02}", sample_id - 32), // Music samples
            _ => format!("sample_{:02}", sample_id),           // Generic samples
        }
    }

    /// Create estimated samples for SF2 raw audio ROM data
    fn create_estimated_samples(&mut self) -> Result<(), AudioExtractionError> {
        info!("Creating estimated audio samples based on SF2 ROM layout");

        let total_size = self.audio_rom.len();
        let estimated_sample_size = total_size / self.total_samples.max(1);

        for sample_id in 0..self.total_samples {
            let sample_offset = sample_id * estimated_sample_size;
            let sample_size = if sample_id == self.total_samples - 1 {
                // Last sample gets remaining data
                total_size - sample_offset
            } else {
                estimated_sample_size
            };

            if sample_size > 0 && sample_offset < total_size {
                let sample_name = Self::generate_sample_name(sample_id as u32);

                self.samples.push(AudioSampleMetadata {
                    id: sample_id as u32,
                    name: sample_name,
                    sample_rate: CPS1_AUDIO_SAMPLE_RATE,
                    channels: CPS1_AUDIO_CHANNELS,
                    bit_depth: CPS1_AUDIO_BIT_DEPTH,
                    data_offset: sample_offset as u32,
                    data_size: sample_size as u32,
                });
            }
        }

        Ok(())
    }
    
    /// Extract a specific audio sample
    pub fn extract_sample(&self, sample_id: u32) -> Result<ExtractedAudioSample, AudioExtractionError> {
        let metadata = self.samples.iter()
            .find(|s| s.id == sample_id)
            .ok_or(AudioExtractionError::InvalidAudioData { offset: 0 })?;
        
        info!("Extracting audio sample: {}", metadata.name);
        
        // Extract raw audio data
        let raw_data = self.extract_raw_audio_data(metadata)?;
        
        // Convert to normalized PCM
        let pcm_data = self.convert_to_pcm(&raw_data, metadata)?;
        
        Ok(ExtractedAudioSample {
            metadata: metadata.clone(),
            pcm_data,
        })
    }
    
    /// Extract raw audio data from ROM
    fn extract_raw_audio_data(&self, metadata: &AudioSampleMetadata) -> Result<Vec<u8>, AudioExtractionError> {
        let start_offset = metadata.data_offset as usize;
        let end_offset = start_offset + metadata.data_size as usize;
        
        if end_offset > self.audio_rom.len() {
            return Err(AudioExtractionError::InsufficientData {
                needed: end_offset,
                available: self.audio_rom.len(),
            });
        }
        
        Ok(self.audio_rom[start_offset..end_offset].to_vec())
    }
    
    /// Convert raw audio data to normalized PCM
    fn convert_to_pcm(&self, raw_data: &[u8], metadata: &AudioSampleMetadata) -> Result<Vec<f32>, AudioExtractionError> {
        match metadata.bit_depth {
            8 => self.convert_8bit_to_pcm(raw_data),
            16 => self.convert_16bit_to_pcm(raw_data),
            _ => {
                // For unsupported bit depths, treat as 8-bit
                warn!("Unsupported bit depth {}, treating as 8-bit", metadata.bit_depth);
                self.convert_8bit_to_pcm(raw_data)
            }
        }
    }
    
    /// Convert 8-bit audio to normalized PCM
    fn convert_8bit_to_pcm(&self, data: &[u8]) -> Result<Vec<f32>, AudioExtractionError> {
        let mut pcm_data = Vec::with_capacity(data.len());
        
        for &sample in data {
            // Convert unsigned 8-bit to signed and normalize to [-1.0, 1.0]
            let signed_sample = sample as i16 - 128;
            let normalized = signed_sample as f32 / 128.0;
            pcm_data.push(normalized.clamp(-1.0, 1.0));
        }
        
        Ok(pcm_data)
    }
    
    /// Convert 16-bit audio to normalized PCM
    fn convert_16bit_to_pcm(&self, data: &[u8]) -> Result<Vec<f32>, AudioExtractionError> {
        if data.len() % 2 != 0 {
            return Err(AudioExtractionError::ConversionFailed {
                reason: "16-bit audio data length must be even".to_string(),
            });
        }
        
        let mut pcm_data = Vec::with_capacity(data.len() / 2);
        
        for chunk in data.chunks_exact(2) {
            // Read 16-bit sample (little-endian)
            let sample = i16::from_le_bytes([chunk[0], chunk[1]]);
            let normalized = sample as f32 / 32768.0;
            pcm_data.push(normalized.clamp(-1.0, 1.0));
        }
        
        Ok(pcm_data)
    }
    
    /// Get all available sample metadata
    pub fn get_all_samples(&self) -> &[AudioSampleMetadata] {
        &self.samples
    }
    
    /// Get sample metadata by ID
    pub fn get_sample_metadata(&self, sample_id: u32) -> Option<&AudioSampleMetadata> {
        self.samples.iter().find(|s| s.id == sample_id)
    }
    
    /// Extract all samples
    pub fn extract_all_samples(&self) -> Result<Vec<ExtractedAudioSample>, AudioExtractionError> {
        let mut extracted_samples = Vec::new();
        
        for metadata in &self.samples {
            match self.extract_sample(metadata.id) {
                Ok(sample) => extracted_samples.push(sample),
                Err(e) => {
                    warn!("Failed to extract sample {}: {}", metadata.name, e);
                    // Continue with other samples
                }
            }
        }
        
        Ok(extracted_samples)
    }
}

/// Convert extracted audio sample to Bevy AudioSource
impl From<ExtractedAudioSample> for Vec<f32> {
    fn from(sample: ExtractedAudioSample) -> Self {
        sample.pcm_data
    }
}

/// Audio manager for handling extracted samples
#[derive(Resource, Default)]
pub struct AudioSampleManager {
    samples: std::collections::HashMap<String, Handle<AudioSource>>,
}

impl AudioSampleManager {
    /// Add a sample to the manager
    pub fn add_sample(&mut self, name: String, handle: Handle<AudioSource>) {
        self.samples.insert(name, handle);
    }
    
    /// Get a sample by name
    pub fn get_sample(&self, name: &str) -> Option<&Handle<AudioSource>> {
        self.samples.get(name)
    }
    
    /// Get all sample names
    pub fn get_sample_names(&self) -> Vec<&String> {
        self.samples.keys().collect()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_8bit_pcm_conversion() {
        let extractor = AudioExtractor::new(vec![]);
        
        // Test conversion of 8-bit audio data
        let test_data = vec![0, 64, 128, 192, 255];
        let pcm_result = extractor.convert_8bit_to_pcm(&test_data).unwrap();
        
        assert_eq!(pcm_result.len(), 5);
        assert!((pcm_result[0] - (-1.0)).abs() < 0.01); // 0 -> -1.0
        assert!((pcm_result[2] - 0.0).abs() < 0.01);    // 128 -> 0.0
        assert!((pcm_result[4] - 0.9921875).abs() < 0.01); // 255 -> ~1.0
    }
    
    #[test]
    fn test_16bit_pcm_conversion() {
        let extractor = AudioExtractor::new(vec![]);
        
        // Test conversion of 16-bit audio data (little-endian)
        let test_data = vec![0x00, 0x80, 0x00, 0x00, 0xFF, 0x7F]; // -32768, 0, 32767
        let pcm_result = extractor.convert_16bit_to_pcm(&test_data).unwrap();
        
        assert_eq!(pcm_result.len(), 3);
        assert!((pcm_result[0] - (-1.0)).abs() < 0.01); // -32768 -> -1.0
        assert!((pcm_result[1] - 0.0).abs() < 0.01);    // 0 -> 0.0
        assert!((pcm_result[2] - 0.999969482421875).abs() < 0.01); // 32767 -> ~1.0
    }
}
