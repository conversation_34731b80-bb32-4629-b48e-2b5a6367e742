//! # Sprite Extractor
//! 
//! Extracts sprite data from Street Fighter II ROM files and converts
//! them to Bevy-compatible formats.

use bevy::{
    prelude::*,
    render::{
        render_asset::RenderAssetUsages,
        render_resource::{Extent3d, TextureDimension, TextureFormat},
    },
};
use sf2_types::endian::rom_reader;
use thiserror::Error;
use log::info;

/// Errors that can occur during sprite extraction
#[derive(Error, Debug)]
pub enum SpriteExtractionError {
    #[error("Invalid sprite data at offset {offset:08x}")]
    InvalidSpriteData { offset: usize },

    #[error("Invalid sprite ID {sprite_id}, maximum is {max_id}")]
    InvalidSpriteId { sprite_id: u32, max_id: u32 },

    #[error("Palette not found: {palette_id}")]
    PaletteNotFound { palette_id: u16 },

    #[error("Sprite too large: {width}x{height}")]
    SpriteTooLarge { width: u32, height: u32 },

    #[error("ROM data too short: needed {needed}, available {available}")]
    InsufficientData { needed: usize, available: usize },
}

/// Sprite metadata extracted from ROM
#[derive(Debug, Clone)]
pub struct SpriteMetadata {
    pub id: u32,
    pub width: u16,
    pub height: u16,
    pub offset_x: i16,
    pub offset_y: i16,
    pub palette_id: u16,
    pub tile_data_offset: u32,
}

/// Color palette for sprites
#[derive(Debug, Clone)]
pub struct SpritePalette {
    pub id: u16,
    pub colors: Vec<Color>,
}

/// Extracted sprite data ready for Bevy
#[derive(Debug)]
pub struct ExtractedSprite {
    pub metadata: SpriteMetadata,
    pub image_data: Vec<u8>, // RGBA8 format
    pub bevy_image: Image,
}

/// CPS1 Graphics ROM structure constants
const CPS1_TILE_SIZE_8X8: usize = 32;    // 8x8 tile = 64 pixels * 4bpp / 8 = 32 bytes
const CPS1_TILE_SIZE_16X16: usize = 128;  // 16x16 tile = 256 pixels * 4bpp / 8 = 128 bytes
const CPS1_TILE_SIZE_32X32: usize = 512;  // 32x32 tile = 1024 pixels * 4bpp / 8 = 512 bytes

/// CPS1 ROM layout for SF2
const SF2_PALETTE_ROM_OFFSET: usize = 0x0;
const SF2_PALETTE_COUNT: usize = 256;
const SF2_PALETTE_SIZE: usize = 32; // 16 colors * 2 bytes (RGB555)

/// SF2 Graphics ROM regions (based on CPS1 architecture)
const SF2_SCROLL1_TILES_OFFSET: usize = 0x8000;   // 8x8 tiles for SCROLL1 layer
const SF2_SCROLL2_TILES_OFFSET: usize = 0x80000;  // 16x16 tiles for SCROLL2 layer
const SF2_SCROLL3_TILES_OFFSET: usize = 0x180000; // 32x32 tiles for SCROLL3 layer
const SF2_OBJ_TILES_OFFSET: usize = 0x280000;     // Object/sprite tiles

/// Sprite extractor for SF2 ROM data
pub struct SpriteExtractor {
    graphics_rom: Vec<u8>,
    palettes: Vec<SpritePalette>,
    tile_count_8x8: usize,
    tile_count_16x16: usize,
    tile_count_32x32: usize,
    obj_tile_count: usize,
}

impl SpriteExtractor {
    /// Create a new sprite extractor
    pub fn new(graphics_rom: Vec<u8>) -> Self {
        // Calculate actual tile counts based on ROM size and structure
        let rom_size = graphics_rom.len();

        // Estimate tile counts based on typical SF2 ROM layout
        let tile_count_8x8 = if rom_size > SF2_SCROLL1_TILES_OFFSET {
            (SF2_SCROLL2_TILES_OFFSET - SF2_SCROLL1_TILES_OFFSET) / CPS1_TILE_SIZE_8X8
        } else { 0 };

        let tile_count_16x16 = if rom_size > SF2_SCROLL2_TILES_OFFSET {
            (SF2_SCROLL3_TILES_OFFSET - SF2_SCROLL2_TILES_OFFSET) / CPS1_TILE_SIZE_16X16
        } else { 0 };

        let tile_count_32x32 = if rom_size > SF2_SCROLL3_TILES_OFFSET {
            (SF2_OBJ_TILES_OFFSET - SF2_SCROLL3_TILES_OFFSET) / CPS1_TILE_SIZE_32X32
        } else { 0 };

        let obj_tile_count = if rom_size > SF2_OBJ_TILES_OFFSET {
            (rom_size - SF2_OBJ_TILES_OFFSET) / CPS1_TILE_SIZE_16X16
        } else { 0 };

        info!("SF2 Graphics ROM Analysis:");
        info!("  ROM Size: {} bytes", rom_size);
        info!("  8x8 Tiles: {} (SCROLL1)", tile_count_8x8);
        info!("  16x16 Tiles: {} (SCROLL2)", tile_count_16x16);
        info!("  32x32 Tiles: {} (SCROLL3)", tile_count_32x32);
        info!("  Object Tiles: {} (OBJ)", obj_tile_count);

        Self {
            graphics_rom,
            palettes: Vec::new(),
            tile_count_8x8,
            tile_count_16x16,
            tile_count_32x32,
            obj_tile_count,
        }
    }

    /// Get total number of extractable sprites/tiles
    pub fn get_total_sprite_count(&self) -> usize {
        // Focus on object tiles for sprites, as these are the character graphics
        self.obj_tile_count
    }
    
    /// Extract all palettes from ROM data
    pub fn extract_palettes(&mut self) -> Result<(), SpriteExtractionError> {
        info!("Extracting SF2 CPS1 palettes from graphics ROM");

        // SF2 palettes are stored at the beginning of graphics ROM in RGB555 format
        for palette_id in 0..SF2_PALETTE_COUNT {
            let offset = SF2_PALETTE_ROM_OFFSET + (palette_id * SF2_PALETTE_SIZE);

            if let Some(palette) = self.extract_palette(offset, palette_id as u16)? {
                self.palettes.push(palette);
            }
        }

        info!("Extracted {} valid palettes from SF2 ROM", self.palettes.len());
        Ok(())
    }
    
    /// Extract a single palette from ROM data
    fn extract_palette(&self, offset: usize, palette_id: u16) -> Result<Option<SpritePalette>, SpriteExtractionError> {
        const PALETTE_SIZE: usize = 16 * 2;
        
        if offset + PALETTE_SIZE > self.graphics_rom.len() {
            return Ok(None);
        }
        
        let mut colors = Vec::with_capacity(16);
        
        for i in 0..16 {
            let color_offset = offset + (i * 2);
            if let Some(rgb555) = rom_reader::read_be_u16(&self.graphics_rom, color_offset) {
                let color = rgb555_to_color(rgb555);
                colors.push(color);
            } else {
                return Ok(None);
            }
        }
        
        // Check if this palette contains any non-black colors (simple validation)
        let has_colors = colors.iter().any(|c| {
            let rgba = c.to_srgba();
            rgba.red > 0.0 || rgba.green > 0.0 || rgba.blue > 0.0
        });
        
        if has_colors {
            Ok(Some(SpritePalette {
                id: palette_id,
                colors,
            }))
        } else {
            Ok(None) // Skip empty/black palettes
        }
    }
    
    /// Extract sprite metadata from ROM
    pub fn extract_sprite_metadata(&self, sprite_id: u32) -> Result<SpriteMetadata, SpriteExtractionError> {
        // Validate sprite ID is within available object tiles
        if sprite_id as usize >= self.obj_tile_count {
            return Err(SpriteExtractionError::InvalidSpriteId {
                sprite_id,
                max_id: self.obj_tile_count as u32 - 1
            });
        }

        // Calculate actual tile offset in OBJ region
        let tile_offset = SF2_OBJ_TILES_OFFSET + (sprite_id as usize * CPS1_TILE_SIZE_16X16);

        // Validate tile data exists
        if tile_offset + CPS1_TILE_SIZE_16X16 > self.graphics_rom.len() {
            return Err(SpriteExtractionError::InsufficientData {
                needed: tile_offset + CPS1_TILE_SIZE_16X16,
                available: self.graphics_rom.len(),
            });
        }

        // SF2 object tiles are typically 16x16 pixels
        // Palette ID is derived from sprite ID (sprites are grouped by character/palette)
        let palette_id = (sprite_id / 64) as u16; // Group sprites by character (64 tiles per character)

        Ok(SpriteMetadata {
            id: sprite_id,
            width: 16,  // CPS1 object tiles are 16x16
            height: 16,
            offset_x: 0,
            offset_y: 0,
            palette_id: palette_id.min(255), // Ensure palette ID is valid
            tile_data_offset: tile_offset as u32,
        })
    }
    
    /// Extract a sprite and convert to Bevy format
    pub fn extract_sprite(&self, sprite_id: u32) -> Result<ExtractedSprite, SpriteExtractionError> {
        let metadata = self.extract_sprite_metadata(sprite_id)?;
        
        // Find the palette for this sprite
        let palette = self.palettes.iter()
            .find(|p| p.id == metadata.palette_id)
            .ok_or(SpriteExtractionError::PaletteNotFound { 
                palette_id: metadata.palette_id 
            })?;
        
        // Extract tile data and convert to RGBA
        let image_data = self.extract_sprite_pixels(&metadata, palette)?;
        
        // Create Bevy image
        let bevy_image = Image::new(
            Extent3d {
                width: metadata.width as u32,
                height: metadata.height as u32,
                depth_or_array_layers: 1,
            },
            TextureDimension::D2,
            image_data.clone(),
            TextureFormat::Rgba8UnormSrgb,
            RenderAssetUsages::RENDER_WORLD,
        );
        
        Ok(ExtractedSprite {
            metadata,
            image_data,
            bevy_image,
        })
    }
    
    /// Extract pixel data for a sprite
    fn extract_sprite_pixels(&self, metadata: &SpriteMetadata, palette: &SpritePalette) -> Result<Vec<u8>, SpriteExtractionError> {
        let pixel_count = (metadata.width as usize) * (metadata.height as usize);
        let mut rgba_data = Vec::with_capacity(pixel_count * 4);
        
        // SF2 sprites are typically 4-bit indexed color (16 colors per palette)
        let tile_data_size = pixel_count / 2; // 4 bits per pixel
        let data_end = metadata.tile_data_offset as usize + tile_data_size;
        
        if data_end > self.graphics_rom.len() {
            return Err(SpriteExtractionError::InsufficientData {
                needed: data_end,
                available: self.graphics_rom.len(),
            });
        }
        
        // Extract 4-bit indexed pixels and convert to RGBA
        for y in 0..metadata.height {
            for x in 0..metadata.width {
                let pixel_index = (y as usize * metadata.width as usize + x as usize) / 2;
                let byte_offset = metadata.tile_data_offset as usize + pixel_index;
                
                if byte_offset < self.graphics_rom.len() {
                    let byte_data = self.graphics_rom[byte_offset];
                    
                    // Extract 4-bit color index (high or low nibble)
                    let color_index = if (x % 2) == 0 {
                        (byte_data >> 4) & 0x0F // High nibble
                    } else {
                        byte_data & 0x0F // Low nibble
                    };
                    
                    // Get color from palette
                    let color = palette.colors.get(color_index as usize)
                        .unwrap_or(&Color::BLACK);
                    
                    // Convert to RGBA8
                    let rgba = color.to_srgba();
                    rgba_data.push((rgba.red * 255.0) as u8);
                    rgba_data.push((rgba.green * 255.0) as u8);
                    rgba_data.push((rgba.blue * 255.0) as u8);
                    rgba_data.push((rgba.alpha * 255.0) as u8);
                } else {
                    // Fallback to transparent black
                    rgba_data.extend_from_slice(&[0, 0, 0, 0]);
                }
            }
        }
        
        Ok(rgba_data)
    }
    
    /// Get the number of available palettes
    pub fn palette_count(&self) -> usize {
        self.palettes.len()
    }
    
    /// Get a palette by ID
    pub fn get_palette(&self, palette_id: u16) -> Option<&SpritePalette> {
        self.palettes.iter().find(|p| p.id == palette_id)
    }
}

/// Convert RGB555 color to Bevy Color
fn rgb555_to_color(rgb555: u16) -> Color {
    let r = ((rgb555 >> 10) & 0x1F) as f32 / 31.0;
    let g = ((rgb555 >> 5) & 0x1F) as f32 / 31.0;
    let b = (rgb555 & 0x1F) as f32 / 31.0;
    
    Color::srgb(r, g, b)
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_rgb555_conversion() {
        // Test white color (all bits set)
        let white_rgb555 = 0x7FFF; // 01111111_11111111
        let white_color = rgb555_to_color(white_rgb555);
        let white_rgba = white_color.to_srgba();
        assert!((white_rgba.red - 1.0).abs() < 0.01);
        assert!((white_rgba.green - 1.0).abs() < 0.01);
        assert!((white_rgba.blue - 1.0).abs() < 0.01);

        // Test black color
        let black_rgb555 = 0x0000;
        let black_color = rgb555_to_color(black_rgb555);
        let black_rgba = black_color.to_srgba();
        assert!(black_rgba.red < 0.01);
        assert!(black_rgba.green < 0.01);
        assert!(black_rgba.blue < 0.01);
    }
}
